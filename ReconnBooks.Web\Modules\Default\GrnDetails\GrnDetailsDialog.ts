import { GrnDetailsForm, GrnDetailsRow, CommoditiesRow, CommodityTypesRow, CommoditiesService } from '@/ServerTypes/Default';
import { Decorators, toId, EditorUtils, WidgetProps } from '@serenity-is/corelib';
import { PendingChangesConfirmGridEditorDialog } from '../../Common/Helpers/PendingChangesConfirmGridEditorDialog';

@Decorators.registerClass('ReconnBooks.Default.GrnDetailsDialog')

export class GrnDetailsDialog extends PendingChangesConfirmGridEditorDialog<GrnDetailsRow> {
    protected getFormKey() { return GrnDetailsForm.formKey; }
    protected getRowDefinition() { return GrnDetailsRow; }
    //protected getService() { return GrnDetailsService.baseUrl; }
    protected form = new GrnDetailsForm(this.idPrefix);

    public PlaceOfSupplyStateId: number;
    isSamePlaceOfSupply: boolean;
    protected commodity: CommoditiesRow;
    private previousCommodityValue: string;
    constructor(props: WidgetProps<any>) {
        super(props);

        this.form.CommodityTypeId.changeSelect2(() => {
            this.setCommodityLabelNames(this.form.CommodityTypeId.text);
        })

        this.form.CommodityCode.changeSelect2(async () => {
            var CommodityCode = toId(this.form.CommodityCode.value);
            if (CommodityCode != null) {
                var Commodity = (await CommoditiesRow.getLookupAsync()).itemById[CommodityCode];
                this.form.CommodityId.value = Commodity.CommodityId.toString();
                this.form.CommodityDescription.value = Commodity.CommodityDescription;
                this.form.CommodityCode.value = Commodity.CommodityId.toString();
            }
        })
    }

    private setCommodityLabelNames(commodityType: string) {
        if (commodityType === "Goods") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Product Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Product Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Product Description';
        } else if (commodityType === "Service") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Service Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Service Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Service Description';
        }
    }

    protected async afterLoadEntity() {
        await super.afterLoadEntity();

        this.previousCommodityValue = this.form.CommodityId.value;

        this.form.CommodityId.change(e => {
            var commodityId = toId(this.form.CommodityId.value);
            if (commodityId != null) {

                CommoditiesService.Retrieve({
                    EntityId: commodityId
                }, response => {
                    var commodity = response.Entity;
                    this.form.CommodityDescription.value = commodity.CommodityDescription;
                    this.form.CommodityCode.value = commodity.CommodityId.toString();
                });

                let currentCommodityValue = this.form.CommodityId.value;
                if (this.previousCommodityValue !== currentCommodityValue) {
                    this.form.ReceivedQuantity.element.focus();
                    this.previousCommodityValue = currentCommodityValue;
                }
            }
            else {
                // clear all fields
                this.clearFields();
            }
        });

        if (!this.isNew()) {
            var commodityType = ((await CommodityTypesRow.getLookupAsync()).itemById[this.form.CommodityTypeId.value]).CommodityType;
            this.form.CommodityCode.value = this.form.CommodityId.value;
            this.form.CommodityTypeId.set_readOnly(true);
            this.setCommodityLabelNames(commodityType);
            EditorUtils.setReadonly(this.form.CommodityTypeId.element.findFirst('.editor'), true);
        }
        this.setDialogsLoadedState();
    }

    protected updateTitle() {
        this.dialogTitle = "Edit GRN Details";
    }
    clearFields() {
        this.form.CommodityDescription.value = undefined;
        this.form.CommodityCode.value = undefined;
        this.form.PoQuantity.value = undefined;
        this.form.ReceivedQuantity.value = undefined;
        this.form.AcceptedQuantity.value = undefined;
        this.form.PoUnitId.value = undefined;
        this.form.AcceptedUnitId.value = undefined;
        this.form.ReceivedUnitId.value = undefined;
        this.form.SupplyDueDate.value = undefined;
        this.form.SerialNos.value = undefined;
        this.form.LocationId.value = undefined;
        this.form.WarehouseId.value = undefined;
        this.form.StoreId.value = undefined;
        this.form.RackId.value = undefined;
        this.form.Remarks.value = undefined;
        this.form.Sku.value = undefined;
    }
}   