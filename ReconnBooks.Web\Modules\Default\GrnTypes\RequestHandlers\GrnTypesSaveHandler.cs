﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.GrnTypesRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.GrnTypesRow;

namespace ReconnBooks.Default;

public interface IGrnTypesSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class GrnTypesSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IGrnTypesSaveHandler
{
    public GrnTypesSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}