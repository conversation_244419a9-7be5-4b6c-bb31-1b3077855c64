using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.GrNsRow>;
using MyRow = ReconnBooks.Default.GrNsRow;

namespace ReconnBooks.Default;

public interface IGrNsListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class GrNsListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IGrNsListHandler
{
    public GrNsListHandler(IRequestContext context)
            : base(context)
    {
    }
}