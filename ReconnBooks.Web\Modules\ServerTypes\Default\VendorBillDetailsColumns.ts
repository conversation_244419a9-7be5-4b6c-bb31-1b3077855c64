﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { VendorBillDetailsRow } from "./VendorBillDetailsRow";

export interface VendorBillDetailsColumns {
    RowNumber: Column<VendorBillDetailsRow>;
    CommodityName: Column<VendorBillDetailsRow>;
    CommodityCode: Column<VendorBillDetailsRow>;
    CommodityType: Column<VendorBillDetailsRow>;
    BillQuantity: Column<VendorBillDetailsRow>;
    UnitName: Column<VendorBillDetailsRow>;
    UnitPrice: Column<VendorBillDetailsRow>;
    UnitAmount: Column<VendorBillDetailsRow>;
    DiscountPercent: Column<VendorBillDetailsRow>;
    DiscountAmount: Column<VendorBillDetailsRow>;
    NetTaxableAmount: Column<VendorBillDetailsRow>;
    GSTRateRemarks: Column<VendorBillDetailsRow>;
    IGSTRate: Column<VendorBillDetailsRow>;
    NetIGSTAmount: Column<VendorBillDetailsRow>;
    CGSTRate: Column<VendorBillDetailsRow>;
    NetCGSTAmount: Column<VendorBillDetailsRow>;
    SGSTRate: Column<VendorBillDetailsRow>;
    NetSGSTAmount: Column<VendorBillDetailsRow>;
    NetAmount: Column<VendorBillDetailsRow>;
    VendorBillId: Column<VendorBillDetailsRow>;
    PurchaseOrderDetailCommodityDescription: Column<VendorBillDetailsRow>;
    CommodityDescription: Column<VendorBillDetailsRow>;
    VendorBillDetailId: Column<VendorBillDetailsRow>;
}

export class VendorBillDetailsColumns extends ColumnsBase<VendorBillDetailsRow> {
    static readonly columnsKey = 'Default.VendorBillDetails';
    static readonly Fields = fieldsProxy<VendorBillDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types