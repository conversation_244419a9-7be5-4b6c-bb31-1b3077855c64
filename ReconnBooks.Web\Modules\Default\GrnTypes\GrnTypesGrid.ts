import { GrnTypesColumns, GrnTypesRow, GrnTypesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { GrnTypesDialog } from './GrnTypesDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.GrnTypesGrid')
@Decorators.filterable()
export class GrnTypesGrid extends EntityGridDialog<GrnTypesRow, any> {
    protected getColumnsKey() { return GrnTypesColumns.columnsKey; }
    protected getDialogType() { return GrnTypesDialog; }
    protected getRowDefinition() { return GrnTypesRow; }
    protected getService() { return GrnTypesService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}