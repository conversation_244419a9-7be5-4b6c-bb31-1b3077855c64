using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.GrnTypes")]
[BasedOnRow(typeof(GrnTypesRow), CheckNames = true)]
public class GrnTypesColumns
{
    //--Serial Numbering--

    [Width(50), AlignCenter]
    public long RowNumber { get; set; }

    [EditLink, DisplayName("GrnType Name"), Width(200)]
    public string GRNTypeName { get; set; }

    [EditLink, DisplayName("Description"), Width(250)]
    public string Description { get; set; }
 
    [EditLink, Width(50), DisplayName("Db.Shared.RecordId"), SortOrder(1, descending: false), AlignCenter]
    public int GRNTypeId { get; set; }
}