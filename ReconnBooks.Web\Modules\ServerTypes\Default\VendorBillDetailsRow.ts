﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface VendorBillDetailsRow {
    RowNumber?: number;
    VendorBillDetailId?: number;
    VendorBillId?: number;
    PurchaseOrderDetailId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    BillQuantity?: number;
    UnitId?: number;
    UnitPrice?: number;
    UnitAmount?: number;
    DiscountPercent?: number;
    DiscountAmount?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    GSTRateId?: number;
    IGSTRate?: number;
    IGSTAmountPerUnit?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    CGSTAmountPerUnit?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    SGSTAmountPerUnit?: number;
    NetSGSTAmount?: number;
    NetAmount?: number;
    PurchaseOrderDetailCommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    UnitName?: string;
    GSTRateRemarks?: string;
    DummyField?: string;
    NetPricePerUnit?: number;
}

export abstract class VendorBillDetailsRow {
    static readonly idProperty = 'VendorBillDetailId';
    static readonly nameProperty = 'VendorBillDetailId';
    static readonly localTextPrefix = 'Default.VendorBillDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<VendorBillDetailsRow>();
}