﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.GrnTypesRow;

namespace ReconnBooks.Default;

public interface IGrnTypesDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class GrnTypesDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IGrnTypesDeleteHandler
{
    public GrnTypesDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}