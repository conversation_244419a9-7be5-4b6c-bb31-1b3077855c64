﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.GrnDetailsRow;

namespace ReconnBooks.Default;

public interface IGrnDetailsDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class GrnDetailsDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IGrnDetailsDeleteHandler
{
    public GrnDetailsDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}