using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.HsnSummary")]
[BasedOnRow(typeof(HsnSummaryRow), CheckNames = true)]
public class HsnSummaryColumns
{
    [DisplayName("Db.Shared.RecordId"), AlignCenter]
    public int HsnSummaryId { get; set; }

    [Hidden]
    [Width(100), LookupEditor(typeof(FinancialYearsRow)), QuickFilter]
    public string FinancialYearName { get; set; }

    [Hidden]
    [Width(70) ]
    public string InvoiceMonth { get; set; }

    [DisplayName("HSN/SAC Code"), Width(120), QuickSearch]
    public string HsnCode { get; set; }

    [DisplayName("HSN/SAC Description"), Width(200)]
    public string HsnDescription { get; set; }

    [DisplayName("Quantity"), Width(70), AlignRight]
    public decimal Quantity { get; set; }

    [DisplayName("UQC"), Width(80), AlignCenter]
    public string UQC { get; set; }

    [DisplayName("Taxable Value"), Width(100), IndianNumberFormatter, AlignRight]
    public decimal TaxableValue { get; set; }

    [DisplayName("CGST Amt."), Width(100), IndianNumberFormatter, AlignRight]
    public decimal Cgst { get; set; }

    [DisplayName("SGST Amt."), Width(100), IndianNumberFormatter,  AlignRight]
    public decimal Sgst { get; set; }

    [DisplayName("IGST Amt."), Width(100), IndianNumberFormatter, AlignRight]
    public decimal Igst { get; set; }

    [Hidden]
    [DisplayName("Cess Amt."), Width(100), IndianNumberFormatter, AlignRight]
    public decimal Cess { get; set; }

    [DisplayName("Net Amount"), Width(100), IndianNumberFormatter,  AlignRight]
    public decimal NetAmount { get; set; }


}