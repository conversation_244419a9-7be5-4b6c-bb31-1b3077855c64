import { GrnTypesForm, GrnTypesRow, GrnTypesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.GrnTypesDialog')
export class GrnTypesDialog extends PendingChangesConfirmDialog<GrnTypesRow> {
    protected getFormKey() { return GrnTypesForm.formKey; }
    protected getRowDefinition() { return GrnTypesRow; }
    protected getService() { return GrnTypesService.baseUrl; }

    protected form = new GrnTypesForm(this.idPrefix);

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        this.setDialogsLoadedState();
    }
}