@using ReconnBooks.Modules.Common.Helpers;
@model ReconnBooks.Modules.Default.GRNs.Reporting.GRNsReportData
@{
    Layout = "";
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GRNs</title>
    <!-- Include Bootstrap CSS from CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0px 0px 10px 15px; /* top right bottom left */
            padding: 0px;
            font-family: Calibri, sans-serif;
        }

        .header-table,
        .PurchaseOrders-table,
        .Vendor-table,
        .Product-table{
            width: 100%; /* Ensures the table takes full width */
            border-collapse: collapse;
            margin-bottom: 0px;
        }
        
        .header-table td,
        .PurchaseOrders-table td,
        .Vendor-table td {
            vertical-align: middle;
        }

        .qr-code {
            width: 80px; /* Fixed width for both logo and QR code */
            height: auto; /* Maintain aspect ratio */
        }

        .logo {
            max-width: 100%; 
            height: auto; 
        }

        .address-container {
            text-align: left;
            word-wrap: break-word; 
        }
        
        .address-container strong {
            font-size: 25px;
            padding: 1px;
        }

        .PurchaseOrders-table td {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            padding: 2px;
            background-color: #f0f0f0;
            border: 0.25px solid black;
        }

        .Vendor-table {
            border: 0.25px solid black;
            border-collapse: collapse;
            width: 100%;
        }

        .Vendor-info-container {
            position: relative;
            padding-bottom: 0px;
            padding-top: 0px;
            margin-top: -20px;
        }

        .Vendor-info {
            vertical-align: top;
            width: 100%;
        }

        .shipping-address-table {
            position: relative;
            width: 100%;
            margin-top: 5px; /* Moves the border closer to GST No */
            border-top: 0.25px solid black; /* Increased thickness */
            border-bottom: none !important;
            border-left: none !important;
            border-right: none !important;
        }

        .Grn-info,
        .GrnNo-info,
        .date-info {
            text-align: left !important;
            border: 0.25px solid black;
        }

        .Grn-info {
            width: 15%;
            padding: 0px;
        }

        .GrnNo-info {
            width: 18%;
            padding: 0px;
        }

        .date-info {
            width: 8%;
            padding: 0px;
        }

        .Grn-info,
        .GrnNo-info,
        .date-info {
            text-align: left;
            border: 0.25px solid black;
            padding: 1px 5px;
            line-height: 1;
            font-size: 16px;
            height: 30px; /* Set a fixed height */
            overflow: hidden; /* Prevents text from increasing row height */
            vertical-align: middle; 
        }

        .entry-line {
            display: flex;
            align-items: flex-start !important;
            padding-left: 2px; 
        }
        
        .entry-line .label {
            display: inline-block;
            width: 135px;
        }
        
        .entry-line .colon {
            display: inline-block;
            width: 5px;
            text-align: center;
        }
        
        .entry-line .value {
            text-align: left;
            padding-left: 5px;
            flex-grow: 1;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .Vendor-table td strong {
            font-weight: bold;
        }

        .header-table .PurchaseOrders-table-cell {
            border: 0.25px solid black;
        }
        
        .header-table {
            table-layout: fixed; 
        }

        .Product-table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
            margin-top: 0px;
            border: 0.25px solid black;
        }
        
        .Product-table td {
            text-align: left;
            border-left: 0.25px solid black; /* Vertical lines only */
            vertical-align: top; /* Align header content at the top */
            line-height: 1.1 !important;
        }

        .Product-table th {
            padding: 2px;
            text-align: center !important;
            border-left: 0.25px solid black;
            font-size: 16px;
            height: 40px !important;
            line-height: 1.1 !important;
        }
        
        .Product-table col {
            width: 10px;
        }
        
        .Product-table .desc-col {
            width: 40px; /* Wider for the description column */
            text-align: left;
        }
        
        .Product-table th:first-child,
        .Product-table td:first-child {
            border-left: none; 
        }
        
        .Product-table td {
            border-top: none; 
            border-bottom: none;
        }
        
        .Product-table th {
            background-color: #f0f0f0;
        }

        .totals-row td {
            background-color: #f0f0f0;
            vertical-align: top; /* Align header content at the top */
        }
        
        .Product-table td, .Product-table th {
            word-wrap: break-word; 
            word-break: break-word; 
            white-space: normal; 
        }

        .footer-note {
            margin-top: 0px;
            text-align: right;
            padding: 8px;
        }

        .header-table {
            width: 100vw;
            margin-left: -20px;
            margin-right: -20px;
            border-collapse: collapse;
        }
        
        .header-table td {
            padding: 2px !important;
            vertical-align: middle;
        }

        .Vendor-info,
        .Grn-info,
        .GrnNo-info,
        .shipping-info {
            padding-left: 3px !important;
        }

        .dynamic-height-row {
            height: 50px; /* Default height that will be multiplied by the number of rows */
        }
        
        .dynamic-height-row td {
            border-left: 0.25px solid black; /* Match the border style of other cells */
            border-right: none;
            border-top: none;
            border-bottom: none;
        }
        
        .dynamic-height-row td:first-child {
            border-left: none;
        }
    
        .Product-table {
            table-layout: fixed; /* Ensure fixed column widths */
            width: 100%;
        }
        
        .Product-table tbody {
            display: table-row-group;
        }
     
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            min-height: 100%;
            height: auto;
            width: 100%;
            box-sizing: border-box;
        }
    </style>
</head>
<body>

    <div class="container-fluid">
        <!-- Header Table -->
        <table class="header-table  ">
            <tr>
                <!-- Logo Column -->
                <td class="col-sm-2 text-center">
                    <img src="/upload/@Model.Client.Logo" alt="" class="logo" style="@(string.IsNullOrWhiteSpace(Model.Client.Logo) ? "display: none;" : "")" />
                </td>
                <!-- Address Column -->
                <td class="col-sm-9 address-container" style="line-height: 1.3!important;">
                    <div style="font-size: 27px; text-align: center;"><strong>@Model.Client.ClientName</strong></div>
                    <div style="font-size: 17px; text-align: center;">
                        <div style="font-weight: bold;">@Model.Client.Address, @Model.Client.CityName - @Model.Client.PINCode<br /></div>

                        <div style="font-weight: bold;">
                            @(string.Join(" | ", new[] {
                            Model.Client.GSTIN != null ? $"GSTIN: {Model.Client.GSTIN}" : null,
                            Model.Client.PlaceOfSupplyStateCode != null ? $"{Model.Client.PlaceOfSupplyStateCode} - {Model.Client.PlaceOfSupplyStateCodeNo}" : null,
                            Model.Client.PAN != null ? $"PAN: {Model.Client.PAN}" : null,
                            Model.Client.UdyamNo != null ? $"UDYAM No.: {Model.Client.UdyamNo}" : null
                            }.Where(x => x != null)))<br />
                        </div>

                        @(string.Join(" | ", new[] {
                        Model.Client.HomePage,
                        Model.Client.EMail != null ? $"Email: {Model.Client.EMail}" : null,
                        Model.Client.PhoneNo != null ? $"Phone: {Model.Client.PhoneNo}" : (Model.Client.MobileNo != null ? $"Phone: {Model.Client.MobileNo}" : null)
                        }.Where(x => x != null)))<br />

                        @(string.Join(" | ", new[] {
                        Model.Client.CINNo != null ? $"CIN: {Model.Client.CINNo}" : null,
                        Model.Client.IECNo != null ? $"IECNo: {Model.Client.IECNo}" : null
                        }.Where(x => x != null)))
                    </div>

                    <div style="font-size: 17px!important; text-align: center; font-weight: bold;">@(Model.Client.TagLine != null ? Model.Client.TagLine : string.Empty)</div>
                </td>

                <!-- QR Code Column -->
                <td class="col-sm-2 text-center">
                    @{
                        if (!string.IsNullOrEmpty(Model.Client.Logo))
                        {
                            <img class="qr-code">
                        }
                    }
                </td>
            </tr>
        </table>

        <table class="PurchaseOrders-table">
            <tr>
                <td colspan="3"> GOODS RECEIVED NOTES</td>
            </tr>
        </table>

        <!-- Vendor Table -->
        <table class="Vendor-table" style="border: 0.25px solid black; border-collapse: collapse; width: 100%;">
            <tr>
                <!-- Vendor Info and Shipping Address Container -->
                <td class="Vendor-info-container" rowspan="7" style="vertical-align: top; width: 48%; border-right: 0.25px solid black;">
                    <div class="Vendor-info" style="font-size: 18px;">
                        <span>Vendor Name:</span><br>
                        <span><strong>@Model.Vendor.VendorName</strong></span><br>
                        <span>
                            @Model.Vendor.BillingAddress<br />
                            @Model.Vendor.BillingCityCityName - @Model.Vendor.BillingPinCode
                        </span><br>
                        <span style="display: block; margin-bottom: 10px;"><strong>GST No.: @Model.Vendor.GSTIN</strong>, @Model.Vendor.PlaceOfSupplyStateName </span>

                        @if (Model.GRNs.DeliveryAddress != null)

                        {
                            <span>Shipping Address:</span>

                            <br />
                            <span style="display: block;">
                                @Model.GRNs.DeliveryAddress,@Model.GRNs.DeliveryCityCityName - @Model.GRNs.DeliveryPinCode
                            </span>
                        }
                    </div>
                </td>
                <!-- Grn Info -->
                <td class="Grn-info" style="font-size: 17px; font-weight: bold;">
                    GRNs No.
                </td>
                <td class="GrnNo-info" style="font-size: 17px; padding: 3px; border-right: none; font-weight: bold;">
                        @Model.GRNs.GRNNo
                </td>
                <td class="date-info" style="font-size: 17px; border-left: none; font-weight: bold;">
                        @Model.GRNs.GRNDate.IndianFormatDate()
                </td>
            </tr>
            <tr>
                <td class="Grn-info" style="font-size: 17px;">
                    Order Ref No.
                </td>
                <td class="GrnNo-info" style="font-size: 17px; border-right: none;">
                    @Model.GRNs.PurchaseOrderNo
                </td>
                <td class="date-info" style="font-size: 17px; border-left: none;">
                    @(Model.PurchaseOrder?.PurchaseOrderDate.IndianFormatDate() ?? string.Empty)
                </td>
            </tr>
            <tr>
                <td class="Grn-info" style="font-size: 17px;">
                    Vendor DC/Inv. No.
                </td>
                <td class="GrnNo-info" style="font-size: 17px; border-right: none;">
                    @Model.GRNs.VendorDcInvoiceNo
                </td>
                <td class="date-info" style="font-size: 17px; border-left: none;">
                    @Model.GRNs.VendorDcInvoiceDate.IndianFormatDate()
                </td>
            </tr>

            <tr>
                <!-- New row for Shipping Address, Docket No., Vehicle No., Payment Terms, and Payment Due Date -->
            <tr>
                <td class="shipping-info" colspan="4" style="vertical-align: top; padding-top: 5px;">
                    <!-- Shipped Via. -->
                        @if (Model.GRNs.ShippedThrough != null)
                        {
                        <div class="entry-line" style="font-size: 17px; display: flex; align-items: center; line-height: 1.5 !important;">
                            <span class="label" style="line-height: 1.5 !important;">Shipped Via.</span>
                            <span class="colon" style="line-height: 1.5 !important; width: 5px;">:</span>
                            <span class="value" style="line-height: 1.5 !important;">@Model.GRNs.ShippedThrough</span>
                        </div>
                        }
                    <!-- Docket No. -->
                        @if (Model.GRNs.ShippingDocketNo != null)
                        {
                        <div class="entry-line" style="font-size: 17px; display: flex; align-items: center; line-height: 1.5 !important;">
                            <span class="label" style="line-height: 1.5 !important;">Docket No.</span>
                            <span class="colon" style="line-height: 1.5 !important; width: 5px;">:</span>
                            <span class="value" style="line-height: 1.5 !important;">@Model.GRNs.ShippingDocketNo</span>
                        </div>
                        }
                    <!-- Vehicle No. -->
                        @if (Model.GRNs.VehicleNo != null)
                        {
                        <div class="entry-line" style="font-size: 17px; display: flex; align-items: center; line-height: 1.5 !important;">
                            <span class="label" style="line-height: 1.5 !important;">Vehicle No.</span>
                            <span class="colon" style="line-height: 1.5 !important; width: 5px;">:</span>
                            <span class="value" style="line-height: 1.5 !important;">@Model.GRNs.VehicleNo</span>
                        </div>
                        }
                    <!-- Gate Pass No. -->
                        @if (Model.GRNs.GatePassNo != null)
                        {
                        <div class="entry-line" style="font-size: 17px; display: flex; align-items: center; line-height: 1.5 !important;">
                            <span class="label" style="line-height: 1.5 !important;">GatePass No.</span>
                            <span class="colon" style="line-height: 1.5 !important; width: 5px;">:</span>
                            <span class="value" style="line-height: 1.5 !important;">@Model.GRNs.GatePassNo</span>
                        </div>
                        }
                    <!-- Received By -->
                        @if (Model.GRNs.ReceivedByEmployeeEmployeeName != null)
                        {
                        <div class="entry-line" style="font-size: 17px; display: flex; align-items: center; line-height: 1.5 !important;">
                            <span class="label" style="line-height: 1.5 !important;">Received By</span>
                            <span class="colon" style="line-height: 1.5 !important; width: 5px;">:</span>
                            <span class="value" style="line-height: 1.5 !important;">@Model.GRNs.ReceivedByEmployeeEmployeeName</span>
                        </div>
                        }
                </td>
            </tr>
        </table>

        <table class="Product-table">
            <colgroup>
                <col style="width: 4px;">
                <col style="width: 49px;">
                <col style="width: 11px;">
                <col style="width: 12px;">
                <col style="width: 12px;">
                <col style="width: 12px;">
            </colgroup>
            <thead>
                <tr>
                    <th>Sl. No.</th>
                    <th>Product/Service Description</th>
                    <th>Received Qty.</th>
                    <th>PO Qty.</th>
                    <th>Accepted Qty.</th>
                    <th>Current stock</th>
                </tr>
            </thead>
            <tbody>
                @{
                    var counter = 1;

                    var details = Model.GRNs.GrnDetailsList;
                }

                @foreach (var GRNsDetail in details)

                {
                    <tr>
                        <td style="font-size: 17px; text-align: center">@(counter++)</td>
                        <td style="width: 90%; text-align: left; font-size: 18px; line-height: 1.1">
                            <strong>@GRNsDetail.CommodityName</strong><br>
                            @if (!string.IsNullOrEmpty(GRNsDetail.CommodityDescription))

                            {
                                <span style="font-size: 16px;">@GRNsDetail.CommodityDescription</span>
                                <br>
                            }
                            @if (!string.IsNullOrEmpty(GRNsDetail.CommodityCode))

                            {
                                <span style="font-size: 16px;">Part No.: @GRNsDetail.CommodityCode</span>
                                <br>
                            }
                        </td>
                        <td style="text-align: center; font-size: 17px">
                            @GRNsDetail.ReceivedQuantity?.ToString("#,##0.##") @GRNsDetail.ReceivedUnitUnitName
                        </td>
                        <td style="text-align: center; font-size: 17px">
                            @GRNsDetail.PoQuantity?.ToString("#,##0.##") @GRNsDetail.PoUnitUnitName
                        </td>
                        <td style="text-align: center; font-size: 17px">
                            @GRNsDetail.AcceptedQuantity?.ToString("#,##0.##") @GRNsDetail.AcceptedUnitUnitName
                        </td>
                        <td style="text-align: center; font-size: 17px"></td>
                    </tr>
                }

                @{
                    var itemCount = details?.Count ?? 0;

                    var minRows = itemCount < 15 ? (15 - itemCount) : 0;
                }

                @for (int i = 0; i < minRows; i++)

                {
                    <tr class="dynamic-height-row">
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                }
            </tbody>

            <tfoot>
                <tr style="line-height: 1px; height: 1px;">
                    @for (int i = 0; i < 6; i++)

                    {
                        <td></td>
                    }
                </tr>
            </tfoot>
        </table>

        <!-- Remarks Section -->
        <table style="width: 100%;  border: 0.25px solid black; border-top: none; border-collapse: collapse; margin-top: 0px;">
            <tr>
                <!-- Remarks Section -->
                <td style="width: 60%; text-align: left; vertical-align: top; padding: 10px; border: none; font-size: 17px;">
                    @Model.GRNs.Remarks
                </td>

                <!-- Authorised Signatory Section -->
                <td style="width: 40%; text-align: center; vertical-align: middle; padding: 10px; border: none;">
                    <div style="display: inline-block; width: 100%; text-align: center; font-size: 17px;">
                        <strong> For @Model.GRNs.ClientName</strong>

                        <!-- Digital Signature (Fixed Space for Image) -->
                        <div id="signature-container" style="margin-top: 10px; margin-bottom: 10px; height: 80px;">
                            <img src='/upload/@Model.Client.ClientDSC'
                                 style='max-width: 300px; max-height: 80px; background-color: transparent; height: 80px; width: auto;'
                                 onerror="this.parentElement.style.height='50px'; this.style.display='none';" />
                        </div>

                        <span style="font-size: 16px;">Authorised Signatory</span>
                    </div>
                </td>
            </tr>
        </table>
        <div class="Powered-by" style="font-size: 12px; margin-top: 2px; word-wrap: break-word;">
            Powered by<strong> www.reconnbooks.com</strong>, hosted by <strong>Reconn Info Solutions India Pvt. Ltd.</strong>
        </div>
    </div>
</body>
</html> 