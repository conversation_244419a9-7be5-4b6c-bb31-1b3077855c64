{
  "Data": {
    "Default": {
      "ConnectionString": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=ReconnBooks_Stage;Integrated Security=True;Connect Timeout=30;Encrypt=False;Trust Server Certificate=False;Application Intent=ReadWrite;Multi Subnet Failover=False",
      //"ConnectionString": "Server=*************; Database=ReconnBooks_Stage; Integrated Security=false; User Id=ReconnUser; Password=***************;Encrypt=false;TrustServerCertificate=true",
      //"ConnectionString": "Server=axiom.centralindia.cloudapp.azure.com; Database=ReconnBooks_Dev; Integrated Security=false; User Id=kiran; Password=January@2025; Encrypt=false; TrustServerCertificate=true",
      //"ConnectionString": "Server=RECONN\\SQLSERVER_2019; Database=ReconnBooks_Stage; Integrated Security=true; Encrypt=false;TrustServerCertificate=true",
      //"ConnectionString": "Server=RECONN\\SQLSERVER_2019; Database=ReconnBooks_Dev; Integrated Security=true; Encrypt=false;TrustServerCertificate=true",

      "ProviderName": "System.Data.SqlClient"

      //"ConnectionString": "Host=*************;Port=5432;Database=ReconnBooks;Username=postgres;Password=***********;Pooling=true;MinPoolSize=5;MaxPoolSize=50;",
      //"ProviderName": "Npgsql",
      //"Dialect": "ReconnBooks.Initialization.PostgresDialectExtended, ReconnBooks.Web"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "BackgroundJobs": {
    "Enabled": true
  },
  "ClamAV": {
    "Enabled": false
  },
  "EnvironmentSettings": {
    "SiteExternalUrl": null
  },
  "SmtpSettings": {
    "Host": "mail.reconnsolutions.com",
    "Port": 587,
    "UseSsl": false,
    "From": "<EMAIL>",
    "PickupPath": "App_Data/Mail"
  },
  "EmailQueueJob": {
    "Enabled": true,
    "AutoUse": true,
    "Interval": 30,
    "BatchSize": 10,
    "LockDuration": 300,
    "RetryLimit": 5
  },
  "CssBundling": {
    "Enabled": false,
    "Minimize": true,
    "UseMinCss": true
  },
  "ScriptBundling": {
    "Enabled": false,
    "Minimize": true,
    "UseMinJS": true
  },
  "Translation": {
    "Enabled": false
  },
  "UploadSettings": {
    "Path": "~/App_Data/upload/"
  },
  "UseForwardedHeaders": true
}