﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.GrnDetailsRow>;
using MyRow = ReconnBooks.Default.GrnDetailsRow;

namespace ReconnBooks.Default;

public interface IGrnDetailsRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class GrnDetailsRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IGrnDetailsRetrieveHandler
{
    public GrnDetailsRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}