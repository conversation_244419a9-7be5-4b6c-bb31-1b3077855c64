using NUglify.JavaScript.Syntax;
using ReconnBooks.Common.RowBehaviors;
using ReconnBooks.Modules.Common.Helpers;
using ReconnBooks.Modules.Default.Clients;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("CreditNotes")]
[DisplayName("Credit Notes"), InstanceName("Credit Notes"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class CreditNotesRow : Row<CreditNotesRow.RowFields>, IIdRow, INameRow, IMultiClientRow, IRowNumberedRow
{
    const string jCustomer = nameof(jCustomer);
    const string jFinancialYear = nameof(jFinancialYear);
    const string jInvoice = nameof(jInvoice);
    const string jSalesReturn = nameof(jSalesReturn);
    const string jPreparedByUser = nameof(jPreparedByUser);
    const string jVerifiedByUser = nameof(jVerifiedByUser);
    const string jAuthorizedByUser = nameof(jAuthorizedByUser);
    const string jModifiedByUser = nameof(jModifiedByUser);
    const string jCancelledByUser = nameof(jCancelledByUser);
    const string jClient = nameof(jClient);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Credit Note Id"), Identity, IdProperty]
    public int? CreditNoteId { get => fields.CreditNoteId[this]; set => fields.CreditNoteId[this] = value; }

    [DisplayName("Credit Note No"), Size(50), NotNull, QuickSearch, NameProperty, Unique]
    public string CreditNoteNo { get => fields.CreditNoteNo[this]; set => fields.CreditNoteNo[this] = value; }

    [DisplayName("Credit Note Date")]
    public DateTime? CreditNoteDate { get => fields.CreditNoteDate[this]; set => fields.CreditNoteDate[this] = value; }

    [DisplayName("Month"), Expression("FORMAT(CreditNoteDate, 'MMM')"), QuickFilter]
    [LookupEditor(typeof(MonthLookup))]
    public string CreditNoteMonth { get => fields.CreditNoteMonth[this]; set => fields.CreditNoteMonth[this] = value; }

    [DisplayName("Customer Name"), NotNull, ForeignKey(typeof(CustomersRow)), LeftJoin(jCustomer), TextualField(nameof(CustomerCompanyName))]
    [ServiceLookupEditor(typeof(CustomersRow), InplaceAdd = true, Service = "Default/Customers/List")]
    public int? CustomerId { get => fields.CustomerId[this]; set => fields.CustomerId[this] = value; }
   
    [DisplayName("Financial Year"), ForeignKey(typeof(FinancialYearsRow)), LeftJoin(jFinancialYear)]
    [TextualField(nameof(FinancialYearName))]
    [LookupEditor(typeof(FinancialYearsRow))]
    public int? FinancialYearId { get => fields.FinancialYearId[this]; set => fields.FinancialYearId[this] = value; }

    [DisplayName("Invoice No."), ForeignKey(typeof(InvoicesRow)), LeftJoin(jInvoice), TextualField(nameof(InvoiceNo))]
    [ServiceLookupEditor(typeof(InvoicesRow), InplaceAdd = true, Service = "Default/Invoices/List",
       CascadeFrom = nameof(CustomerId), CascadeValue = nameof(CustomerId))]
    public int? InvoiceId { get => fields.InvoiceId[this]; set => fields.InvoiceId[this] = value; }

    [DisplayName("Sales Return"), ForeignKey(typeof(SalesReturnsRow)), LeftJoin(jSalesReturn), TextualField(nameof(SalesReturnNo))]
    [ServiceLookupEditor(typeof(SalesReturnsRow), InplaceAdd = true, Service = "Default/SalesReturns/List",
        CascadeFrom = nameof(InvoiceId), CascadeValue = nameof(InvoiceId))]
    public int? SalesReturnId { get => fields.SalesReturnId[this]; set => fields.SalesReturnId[this] = value; }

    //-------------------------------------------------------------------------
    [MasterDetailRelation(foreignKey: nameof(CreditNoteDetailsRow.CreditNoteId)), NotMapped]
    public List<CreditNoteDetailsRow> CreditNoteDetailsList
    {
        get { return Fields.CreditNoteDetailsList[this]; }
        set { Fields.CreditNoteDetailsList[this] = value; }
    }
    //-------------------------------------------------------------------------
   
    [DisplayName("Net Taxable Amt."), Size(18), Scale(2), NotNull, NotMapped]
    public decimal? NetTaxableAmount { get => fields.NetTaxableAmount[this]; set => fields.NetTaxableAmount[this] = value; }

    [DisplayName("Net CGST Amt."), Column("NetCGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetCGSTAmount { get => fields.NetCGSTAmount[this]; set => fields.NetCGSTAmount[this] = value; }

    [DisplayName("Net SGST Amt."), Column("NetSGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetSGSTAmount { get => fields.NetSGSTAmount[this]; set => fields.NetSGSTAmount[this] = value; }

    [DisplayName("Net IGST Amt."), Column("NetIGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetIGSTAmount { get => fields.NetIGSTAmount[this]; set => fields.NetIGSTAmount[this] = value; }

    [DisplayName("Place of Supply")]
    [Origin(jCustomer, nameof(CustomersRow.PlaceOfSupplyStateName)), LookupInclude]
    public string PlaceOfSupplyStateName { get => fields.PlaceOfSupplyStateName[this]; set => fields.PlaceOfSupplyStateName[this] = value; }

    [DisplayName("Credit Note Amount"), Size(18), Scale(2), QuickSearch]
    public decimal? CreditNoteAmount { get => fields.CreditNoteAmount[this]; set => fields.CreditNoteAmount[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }

    [DisplayName("Client"), NotNull, ForeignKey(typeof(ClientsRow)), LeftJoin(jClient), TextualField(nameof(ClientName))]
    [ServiceLookupEditor(typeof(ClientsRow), Service = "Default/Clients/List")]
    [Insertable(false), Updatable(false)] //add for MultiTenancy 
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [DisplayName("Client"), Origin(jClient, nameof(ClientsRow.ClientName))]
    public string ClientName { get => fields.ClientName[this]; set => fields.ClientName[this] = value; }
    public Int32Field ClientIdField => fields.ClientId; //add for MultiTenancy 

    [DisplayName("Prepared By User"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jPreparedByUser)]
    [TextualField(nameof(PreparedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? PreparedByUserId { get => fields.PreparedByUserId[this]; set => fields.PreparedByUserId[this] = value; }

    [DisplayName("Prepared Date"), DateTimeEditor]
    public DateTime? PreparedDate { get => fields.PreparedDate[this]; set => fields.PreparedDate[this] = value; }

    [DisplayName("Verified By User"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jVerifiedByUser)]
    [TextualField(nameof(VerifiedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? VerifiedByUserId { get => fields.VerifiedByUserId[this]; set => fields.VerifiedByUserId[this] = value; }

    [DisplayName("Verified Date"), DateTimeEditor]
    public DateTime? VerifiedDate { get => fields.VerifiedDate[this]; set => fields.VerifiedDate[this] = value; }

    [DisplayName("Authorized By User"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jAuthorizedByUser)]
    [TextualField(nameof(AuthorizedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? AuthorizedByUserId { get => fields.AuthorizedByUserId[this]; set => fields.AuthorizedByUserId[this] = value; }

    [DisplayName("Authorized Date"), DateTimeEditor]
    public DateTime? AuthorizedDate { get => fields.AuthorizedDate[this]; set => fields.AuthorizedDate[this] = value; }

    [DisplayName("Modified By User"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jModifiedByUser)]
    [TextualField(nameof(ModifiedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? ModifiedByUserId { get => fields.ModifiedByUserId[this]; set => fields.ModifiedByUserId[this] = value; }

    [DisplayName("Modified Date")]
    public DateTime? ModifiedDate { get => fields.ModifiedDate[this]; set => fields.ModifiedDate[this] = value; }

    [DisplayName("Cancelled By User"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jCancelledByUser)]
    [TextualField(nameof(CancelledByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? CancelledByUserId { get => fields.CancelledByUserId[this]; set => fields.CancelledByUserId[this] = value; }

    [DisplayName("Cancelled Date")]
    public DateTime? CancelledDate { get => fields.CancelledDate[this]; set => fields.CancelledDate[this] = value; }

    [DisplayName("Authorization Status"), NotNull]
    public bool? AuthorizedStatus { get => fields.AuthorizedStatus[this]; set => fields.AuthorizedStatus[this] = value; }

    [DisplayName("Customer Company Name"), Origin(jCustomer, nameof(CustomersRow.CompanyName)), QuickSearch]
    public string CustomerCompanyName { get => fields.CustomerCompanyName[this]; set => fields.CustomerCompanyName[this] = value; }

    [DisplayName("Financial Year"), Origin(jFinancialYear, nameof(FinancialYearsRow.FinancialYearName))]
    public string FinancialYearName { get => fields.FinancialYearName[this]; set => fields.FinancialYearName[this] = value; }

    [DisplayName("Invoice Invoice No"), Origin(jInvoice, nameof(InvoicesRow.InvoiceNo))]
    public string InvoiceNo { get => fields.InvoiceNo[this]; set => fields.InvoiceNo[this] = value; }

    [DisplayName("Sales Return Sales Return No"), Origin(jSalesReturn, nameof(SalesReturnsRow.SalesReturnNo))]
    public string SalesReturnNo { get => fields.SalesReturnNo[this]; set => fields.SalesReturnNo[this] = value; }

    [DisplayName("Prepared By User Username"), Origin(jPreparedByUser, nameof(Administration.UserRow.Username))]
    public string PreparedByUserUsername { get => fields.PreparedByUserUsername[this]; set => fields.PreparedByUserUsername[this] = value; }

    [DisplayName("Verified By User Username"), Origin(jVerifiedByUser, nameof(Administration.UserRow.Username))]
    public string VerifiedByUserUsername { get => fields.VerifiedByUserUsername[this]; set => fields.VerifiedByUserUsername[this] = value; }

    [DisplayName("Authorized By User Username"), Origin(jAuthorizedByUser, nameof(Administration.UserRow.Username))]
    public string AuthorizedByUserUsername { get => fields.AuthorizedByUserUsername[this]; set => fields.AuthorizedByUserUsername[this] = value; }

    [DisplayName("Modified By User Username"), Origin(jModifiedByUser, nameof(Administration.UserRow.Username))]
    public string ModifiedByUserUsername { get => fields.ModifiedByUserUsername[this]; set => fields.ModifiedByUserUsername[this] = value; }

    [DisplayName("Cancelled By User Username"), Origin(jCancelledByUser, nameof(Administration.UserRow.Username))]
    public string CancelledByUserUsername { get => fields.CancelledByUserUsername[this]; set => fields.CancelledByUserUsername[this] = value; }
}