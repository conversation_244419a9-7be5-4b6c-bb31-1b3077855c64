﻿import { StringEditor, ServiceLookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { UqCsDialog } from "../../Default/UqCs/UqCsDialog";

export interface UnitsForm {
    UnitName: StringEditor;
    UnitDescription: StringEditor;
    UqcId: ServiceLookupEditor;
}

export class UnitsForm extends PrefixedContext {
    static readonly formKey = 'Default.Units';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!UnitsForm.init)  {
            UnitsForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;

            initFormType(UnitsForm, [
                'UnitName', w0,
                'UnitDescription', w0,
                'UqcId', w1
            ]);
        }
    }
}

queueMicrotask(() => [UqCsDialog]); // referenced dialogs