import { CitiesColumns, CitiesRow, CitiesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, gridPageInit } from '@serenity-is/corelib';
import { CitiesDialog } from './CitiesDialog';
import { ExcelExportHelper } from "@serenity-is/extensions";
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.CitiesGrid')
export class CitiesGrid extends EntityGridDialog<CitiesRow, any> {
    protected getColumnsKey() { return CitiesColumns.columnsKey; }
    protected getDialogType() { return CitiesDialog; }
    protected getRowDefinition() { return CitiesRow; }
    protected getService() { return CitiesService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }

    protected getButtons() {
        let buttons = super.getButtons();
        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: CitiesService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));
        return buttons;
    }

    protected onDialogClose(): void {
        super.onDialogClose();

        // Set focus on the add button after dialog closes
        setTimeout(() => {
            // Try to find the add button using DOM querySelector
            const gridElement = this.element?.[0] || this.element;
            if (gridElement) {
                // Look for add button by common selectors
                let addButton = gridElement.querySelector('.add-button') as HTMLElement;
                if (!addButton) {
                    addButton = gridElement.querySelector('button[title*="Add"]') as HTMLElement;
                }
                if (!addButton) {
                    addButton = gridElement.querySelector('.s-Toolbar button:first-child') as HTMLElement;
                }

                if (addButton) {
                    addButton.focus();
                }
            }
        }, 100);
    }

}