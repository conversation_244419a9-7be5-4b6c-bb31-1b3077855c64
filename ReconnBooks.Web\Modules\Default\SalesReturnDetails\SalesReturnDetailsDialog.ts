import { SalesReturnDetailsForm, SalesReturnDetailsRow, CommoditiesRow, CommodityTypesRow, CommoditiesService } from '@/ServerTypes/Default';
import { Decorators, toId, EditorUtils, getRemoteData, WidgetProps } from '@serenity-is/corelib';
import { PendingChangesConfirmGridEditorDialog } from '../../Common/Helpers/PendingChangesConfirmGridEditorDialog';

@Decorators.registerClass('ReconnBooks.Default.SalesReturnDetailsDialog')
@Decorators.responsive()
export class SalesReturnDetailsDialog extends PendingChangesConfirmGridEditorDialog<SalesReturnDetailsRow> {
    protected getFormKey() { return SalesReturnDetailsForm.formKey; }
    protected getRowDefinition() { return SalesReturnDetailsRow; }
    //protected getService() { return SalesReturnDetailsService.baseUrl; }

    protected form = new SalesReturnDetailsForm(this.idPrefix);
    public IsSamePlaceOfSupply: boolean;
    protected commodity: CommoditiesRow;
    private previousCommodityValue: string; 
    constructor(props?: any) {
        super(props);

        this.form.CommodityTypeId.changeSelect2(() => {
            this.setCommodityLabelNames(this.form.CommodityTypeId.text);
        })
            
        this.form.CommodityCode.changeSelect2(async () => {
            var CommodityCode = toId(this.form.CommodityCode.value);
            if (CommodityCode != null) {
                var Commodity = (await CommoditiesRow.getLookupAsync()).itemById[CommodityCode];
                this.form.CommodityId.value = Commodity.CommodityId.toString();
                this.form.CommodityDescription.value = Commodity.CommodityDescription;
                this.form.CommodityCode.value = Commodity.CommodityId.toString();
            }
        });
    }

    private setCommodityLabelNames(commodityType: string) {
        if (commodityType === "Goods") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Product Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Product Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Product Description';
        } else if (commodityType === "Service") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Service Code';
            this.form.CommodityId.element[0].parentElement.querySelector('label').textContent = 'Service Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Service Description';
        }
    }

    protected async afterLoadEntity() {
        await super.afterLoadEntity();

        this.previousCommodityValue = this.form.CommodityId.value;

        this.form.CommodityId.change(e => {
            var commodityId = toId(this.form.CommodityId.value);
            if (commodityId != null) {

                CommoditiesService.Retrieve({
                    EntityId: commodityId
                }, response => {
                    var commodity = response.Entity;
                    this.form.CommodityDescription.value = commodity.CommodityDescription;
                    this.form.CommodityCode.value = commodity.CommodityId.toString();
                });

                let currentCommodityValue = this.form.CommodityId.value;
                if (this.previousCommodityValue !== currentCommodityValue) {
                    this.form.Quantity.element.focus();
                    this.previousCommodityValue = currentCommodityValue;
                }
            }
            else {
                // clear all fields
                this.clearFields();
            }
        });

        if (this.IsSamePlaceOfSupply) {
            this.hideIGST();
            this.showCGST();
            this.showSGST();
        }
        else {
            this.showIGST();
            this.hideCGST();
            this.hideSGST();
        }

        if (!this.isNew()) {
            var commodityType = ((await CommodityTypesRow.getLookupAsync()).itemById[this.form.CommodityTypeId.value]).CommodityType;
            this.form.CommodityCode.value = this.form.CommodityId.value;
            this.form.CommodityTypeId.set_readOnly(true);
            this.setCommodityLabelNames(commodityType);
            EditorUtils.setReadonly(this.form.CommodityTypeId.element.findFirst('.editor'), true);
        }
         this.setDialogsLoadedState();
    }

    //------------Showing and Hiding GST Rates---------
    showCGST() {
        this.element.findFirst(".CgstRate").show();
        this.element.findFirst(".CgstAmountPerUnit").show();
        this.element.findFirst(".NetCgstAmount").show();
    }

    hideCGST() {
        this.element.findFirst(".CgstRate").hide();
        this.element.findFirst(".CgstAmountPerUnit").hide();
        this.element.findFirst(".NetCgstAmount").hide();
    }

    showSGST() {
        this.element.findFirst(".SgstRate").show();
        this.element.findFirst(".SgstAmountPerUnit").show();
        this.element.findFirst(".NetSgstAmount").show();
    }

    hideSGST() {
        this.element.findFirst(".SgstRate").hide();
        this.element.findFirst(".SgstAmountPerUnit").hide();
        this.element.findFirst(".NetSgstAmount").hide();
    }

    showIGST() {
        this.element.findFirst(".IgstRate").show();
        this.element.findFirst(".IgstAmountPerUnit").show();
        this.element.findFirst(".NetIgstAmount").show();
    }

    hideIGST() {
        this.element.findFirst(".IgstRate").hide();
        this.element.findFirst(".IgstAmountPerUnit").hide();
        this.element.findFirst(".NetIgstAmount").hide();
    }

    protected updateTitle() {
        this.dialogTitle = "Edit Sales Return Details";
    }

    clearFields() {
        this.form.CommodityCode.value = undefined;
        this.form.CommodityDescription.value = undefined;
        this.form.RejectedQuantity.value = undefined;
        this.form.RejectedUnitId.value = undefined;
        this.form.RejectedItemSerialNo.value = undefined;
        this.form.RejectionReasonId.value = undefined;
        this.form.InvoiceQuantity.value = undefined;
        this.form.InvoiceUnitId.value = undefined;
        this.form.NetPricePerUnit.value = undefined;
        this.form.NetAmount.value = undefined;
        this.form.AssessmentRemarks.value = undefined;
        this.form.ReplacementMethodId.value = undefined;
        this.form.Remarks.value = undefined;
    }
 }