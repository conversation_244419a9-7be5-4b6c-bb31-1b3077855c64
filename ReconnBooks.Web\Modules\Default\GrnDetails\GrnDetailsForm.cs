using Serenity.ComponentModel;
using System;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.GrnDetails")]
[BasedOnRow(typeof(GrnDetailsRow), CheckNames = true)]
public class GrnDetailsForm
{
    [Category("Commodity Details"), Collapsible]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Commodity Type")]
    public int CommodityTypeId { get; set; }

    //--Products and Services--

    [DisplayName("Product Code")]
    public string CommodityCode { get; set; }

    [FullWidth]
    [DisplayName("Product Name")]
    public long CommodityId { get; set; }

    [TextAreaEditor(Rows = 3)]
    [DisplayName("Product Description")]
    public string CommodityDescription { get; set; }

    [Category("Goods Received Details"), Collapsible]
    [OneThirdWidth(UntilNext = true)]
    [DisplayName("Received Qty."), LabelWidth(110)]
    public decimal ReceivedQuantity { get; set; }

    [ReadOnly(true)]
    [DisplayName("PO Qty."), LabelWidth(100)]
    public decimal PoQuantity { get; set; }

    [ReadOnly(true)]
    [DisplayName("Accepted Qty."), LabelWidth(110)]     //Accepted Qty. is same as Received Qty. and Readonly for the time being.
    public decimal AcceptedQuantity { get; set; }       //GRN status to be "Closed" when the Accepted Qty.= Received Qty. else, Pending
                                                        //If Received Qty.= PO Qty, then "Purchase Order" status to be "Closed"

    [DisplayName("Received Unit"), LabelWidth(110)]
    public int ReceivedUnitId { get; set; }

    [ReadOnly(true)]
    [DisplayName("PO Unit"), LabelWidth(100)]
    public int PoUnitId { get; set; }


    [ReadOnly(true)]
    [DisplayName("Accepted Unit"), LabelWidth(110)]
    public int AcceptedUnitId { get; set; }

    
    [Category("Store Details"), Collapsible]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Sup. Due Date"), LabelWidth(100)]
    public DateTime SupplyDueDate { get; set; }

    [DisplayName("SKUs"), LabelWidth(100)]
    public string Sku { get; set; }

    [FullWidth(UntilNext = true)]
    [DisplayName("Serial Nos."), LabelWidth(100)]
    public string SerialNos { get; set; }

    [HalfWidth(UntilNext = true)]
    [DisplayName("Location"), LabelWidth(100)]
    public int LocationId { get; set; }

    [DisplayName("Warehouse"), LabelWidth(100)]
    public int WarehouseId { get; set; }

    [DisplayName("Store Name"), LabelWidth(100)]
    public int StoreId { get; set; }

    [DisplayName("Rack Name"), LabelWidth(100)]
    public int RackId { get; set; }

    [FullWidth]
    [DisplayName("Remarks"), LabelWidth(100)]
    public string Remarks { get; set; }

    //  public int ClientId { get; set; }
    [Hidden]
    public int PurchaseOrderDetailId { get; set; }
}