import { GrNsColumns, GrNsRow, GrNsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, LookupEditor, WidgetProps, notifySuccess, notifyError } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { ExcelExportHelper, PdfExportHelper, ReportHelper } from "@serenity-is/extensions";
import { HeaderFiltersMixin } from "@serenity-is/pro.extensions";
import { GrNsDialog } from './GrNsDialog';

@Decorators.registerClass('ReconnBooks.Default.GrNsGrid')
@Decorators.filterable()
export class GrNsGrid extends EntityGrid<GrNsRow, any> {
    protected getColumnsKey() { return GrNsColumns.columnsKey; }
    protected getDialogType() { return GrNsDialog; }
    protected getRowDefinition() { return GrNsRow; }
    protected getService() { return GrNsService.baseUrl; }

    constructor(props: WidgetProps<any>) {
        super(props);

        new HeaderFiltersMixin({
            grid: this
        });
    }

    protected async createQuickFilters(): Promise<void> {
        await super.createQuickFilters();

        const currentFinancialYearId = await FinancialYearHelper.getCurrentFinancialYearId();
        if (currentFinancialYearId) {
            this.findQuickFilter(LookupEditor, "FinancialYearId").values = [currentFinancialYearId.toString()];
        }
    }

    protected getColumns() {
        var columns = super.getColumns();

        columns.splice(1, 0, {
            id: 'Print GRNs',
            field: null,
            name: '',
            cssClass: 'align-center',
            headerCssClass: 'no-header-filter',
            format: _ => `<a class="inline-action" data-action="print-GRNs" title="GRNs"><i class="fas fa-file-pdf" style="color: red"></i></a>`,
            width: 20,
            minWidth: 20,
            maxWidth: 20
        });

        columns.splice(2, 0, {
            id: 'Email GRNs',
            field: null,
            name: '',
            cssClass: 'align-center',
            headerCssClass: 'no-header-filter',
            format: _ => `<a class="inline-action" data-action="email-GRNs" title="email GRNs"><i class="fas fa-envelope" style="color: #4CAF50"></i></a>`,
            width: 20,
            minWidth: 20,
            maxWidth: 20
        });

        return columns;
    }

    protected onClick(e: Event, row: number, cell: number) {
        super.onClick(e, row, cell);

        //if (Fluent.isDefaultPrevented(e))
        //    return;

        var item = this.itemAt(row);
        let action = (e.target as HTMLElement)?.closest(".inline-action")?.getAttribute("data-action");
        if (action) {
            e.preventDefault();
            if (action == "print-GRNs") {
                ReportHelper.execute({
                    reportKey: 'GRNReport',
                    params: {
                        ID: item.GRNId
                    }
                });
            }
            else if (action == "email-GRNs") {
                this.emailGRNs(item);
            }
        }
    }

    private async emailGRNs(item: GrNsRow) {
        try {
            await GrNsService.EmailGRN({
                DocumentId: item.GRNId,
                DocumentNo: item.GRNNo,
                ToEmail: item.VendorEMailId
            });
            notifySuccess("GRN has been emailed successfully");
        }
        catch (e) {
            notifyError(e.message);
        }
    }

    protected getButtons() {
        var buttons = super.getButtons();
        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: GrNsService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));
        buttons.push(PdfExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            title: "PDF"
        }));
        return buttons;
    }
}