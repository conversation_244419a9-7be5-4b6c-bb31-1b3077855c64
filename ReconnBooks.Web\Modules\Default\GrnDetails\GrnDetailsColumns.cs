using Serenity.ComponentModel;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.GrnDetails")]
[BasedOnRow(typeof(GrnDetailsRow), CheckNames = true)]
public class GrnDetailsColumns
{
    [DisplayName(""), Width(30), AlignCenter]
    public long RowNumber { get; set; }
  
    [EditLink, DisplayName("Product/Service Name"), Width(200)]
    public string CommodityName { get; set; }

    [DisplayName("Code"), Width(90)]
    public string CommodityCode { get; set; }

    [EditLink, DisplayName("Type"), Width(60), LookupInclude]
    public string CommodityType { get; set; }

    [EditLink, DisplayName("GRN Qty."), Width(60), IndianNumberFormatter, AlignCenter]
    public decimal ReceivedQuantity { get; set; }

    [DisplayName("Unit"), <PERSON>idth(60)]
    public string ReceivedUnitUnitName { get; set; }

    [DisplayName("PO Qty."), Width(60), <PERSON>gn<PERSON>enter]
    public decimal PoQuantity { get; set; }

    [DisplayName("Unit"), Width(60)]
    public string PoUnitUnitName { get; set; }

    [Hidden]
    [EditLink, DisplayName("Accepted Qty."), Width(60), AlignCenter]
    public decimal AcceptedQuantity { get; set; }

    [Hidden]
    [DisplayName("Unit"), Width(60)]
    public string AcceptedUnitUnitName { get; set; }

    [Hidden]
    [DisplayName("SKU"), Width(60)]
    public string Sku { get; set; }

    [Hidden]
    [DisplayName("Serial Nos."), Width(100)]
    public string SerialNos { get; set; }

    [DisplayName("Due Date"), Width(80)]
    public DateTime SupplyDueDate { get; set; }

    [Hidden]
    [DisplayName("Location Name"), Width(100)]
    public string LocationName { get; set; }

    [Hidden]
    [DisplayName("Warehouse Name"), Width(100)]
    public string WarehouseName { get; set; }

    [DisplayName("Store Name"), Width(100),]
    public string StoreName { get; set; }

    [Hidden]
    public string RackNo { get; set; }

    [Hidden]
    public string Remarks { get; set; }

    [EditLink, DisplayName("Description"), Width(250)]
    public string CommodityDescription { get; set; }

    [Hidden]
    public string PurchaseOrderDetailCommodityDescription { get; set; }

    [Hidden]
    public string GRNNo { get; set; }

    [EditLink, DisplayName("Db.Shared.RecordId"), AlignCenter]
    public int GrnDetailId { get; set; }

}