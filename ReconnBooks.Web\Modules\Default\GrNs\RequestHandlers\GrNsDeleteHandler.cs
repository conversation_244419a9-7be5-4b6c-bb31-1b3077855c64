using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.GrNsRow;

namespace ReconnBooks.Default;

public interface IGrNsDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class GrNsDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IGrNsDeleteHandler
{
    public GrNsDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}