﻿import { StringEditor, IntegerEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface SupplyTypesForm {
    SupplyType: StringEditor;
    NatureOfSupplyId: IntegerEditor;
}

export class SupplyTypesForm extends PrefixedContext {
    static readonly formKey = 'Default.SupplyTypes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!SupplyTypesForm.init)  {
            SupplyTypesForm.init = true;

            var w0 = StringEditor;
            var w1 = IntegerEditor;

            initFormType(SupplyTypesForm, [
                'SupplyType', w0,
                'NatureOfSupplyId', w1
            ]);
        }
    }
}