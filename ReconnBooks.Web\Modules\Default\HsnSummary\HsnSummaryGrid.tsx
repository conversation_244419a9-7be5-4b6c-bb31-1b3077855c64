import { Decorators, EntityGrid, LookupEditor } from '@serenity-is/corelib';
import { HsnSummaryColumns, HsnSummaryRow, HsnSummaryService } from '../../ServerTypes/Default';
import { HsnSummaryDialog } from './HsnSummaryDialog';
import { ExcelExportHelper, PdfExportHelper } from '@serenity-is/extensions';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';

@Decorators.registerClass('ReconnBooks.Default.HsnSummaryGrid')
export class HsnSummaryGrid extends EntityGrid<HsnSummaryRow> {
    protected getColumnsKey() { return HsnSummaryColumns.columnsKey; }
    protected getRowDefinition() { return HsnSummaryRow; }
    protected getService() { return HsnSummaryService.baseUrl; }

    protected getButtons() {
        var buttons = super.getButtons();

        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: HsnSummaryService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));

        buttons.push(PdfExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            title: "PDF"
        }));

        return buttons;
    }

    protected async createQuickFilters(): Promise<void> {
        await super.createQuickFilters();

        // Set current financial year
        const currentFinancialYearId = await FinancialYearHelper.getCurrentFinancialYearId();
        if (currentFinancialYearId) {
            this.findQuickFilter(LookupEditor, "FinancialYearId").values = [currentFinancialYearId.toString()];
        }

        // Set current month
        const currentMonth = new Date().toLocaleString('default', { month: 'short' }); 
        this.findQuickFilter(LookupEditor, "InvoiceMonth").values = [currentMonth];
    }
}