using Microsoft.AspNetCore.Mvc;
using ReconnBooks.AppServices;
using Serenity.Data;
using Serenity.Reporting;
using Serenity.Services;
using Serenity.Web;
using System;
using System.Data;
using System.Globalization;
using MyRow = ReconnBooks.Default.HsnSummaryRow;

namespace ReconnBooks.Default.Endpoints;

[Route("Services/Default/HsnSummary/[action]")]
[ConnectionKey(typeof(MyRow)), ServiceAuthorize(typeof(MyRow))]
public class HsnSummaryEndpoint : ServiceEndpoint
{
    [HttpPost, AuthorizeCreate(typeof(MyRow))]
    public SaveResponse Create(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IHsnSummarySaveHandler handler)
    {
        return handler.Create(uow, request);
    }

    [HttpPost, AuthorizeUpdate(typeof(MyRow))]
    public SaveResponse Update(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IHsnSummarySaveHandler handler)
    {
        return handler.Update(uow, request);
    }

    [HttpPost, AuthorizeDelete(typeof(MyRow))]
    public DeleteResponse Delete(IUnitOfWork uow, DeleteRequest request,
        [FromServices] IHsnSummaryDeleteHandler handler)
    {
        return handler.Delete(uow, request);
    }

    [HttpPost]
    public RetrieveResponse<MyRow> Retrieve(IDbConnection connection, RetrieveRequest request,
        [FromServices] IHsnSummaryRetrieveHandler handler)
    {
        return handler.Retrieve(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public ListResponse<MyRow> List(IDbConnection connection, ListRequest request,
        [FromServices] IHsnSummaryListHandler handler,
        [FromServices] IUserAccessor userAccessor,
        [FromServices] IUserRetrieveService userRetriever)
    {
        var response = new ListResponse<MyRow>();

        if (userAccessor.User?.GetUserDefinition(userRetriever) is UserDefinition user)
        {
            var clientId = user.ClientId;

            // Extract financialYearId and invoiceMonth filters from request
            int? financialYearId = null;
            string invoiceMonth = null;
            string searchText = null;

            if (request.EqualityFilter != null)
            {
                // Check for financialYearId filter
                if (request.EqualityFilter.TryGetValue("FinancialYearId", out object fyValue) &&
                    fyValue != null && !string.IsNullOrEmpty(fyValue.ToString()))
                {
                    // Try to parse the value safely
                    if (int.TryParse(fyValue.ToString(), out int fyId))
                    {
                        financialYearId = fyId;
                    }
                }

                // Check for invoiceMonth filter
                if (request.EqualityFilter.TryGetValue("InvoiceMonth", out object imValue) &&
                    imValue != null && !string.IsNullOrEmpty(imValue.ToString()))
                {
                    invoiceMonth = imValue.ToString();
                }
            }

            // Get search text for quick search
            if (!string.IsNullOrEmpty(request.ContainsText))
            {
                searchText = request.ContainsText;
            }

            string sql = @"
            SELECT
                ROW_NUMBER() OVER (ORDER BY hsc.HSNSACCode) as HsnSummaryId,
                i.FinancialYearId as FinancialYearId,
                fy.FinancialYearName as FinancialYearName,
                FORMAT(i.InvoiceDate, 'MMM') as InvoiceMonth,
                hsc.HSNSACCode as HsnCode,
                hsc.HSNSACDescription as HsnDescription,
                FORMAT(SUM(id.Quantity), '#,##0.##') as Quantity,
                u.UnitName as UQC,
                SUM(id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) as TaxableValue,
                SUM(
                    CASE
                        WHEN c.PlaceOfSupplyId = cl.PlaceOfSupplyId THEN (id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) * gr.CGSTPercent / 100
                        ELSE 0
                    END
                ) as Cgst,
                SUM(
                    CASE
                        WHEN c.PlaceOfSupplyId = cl.PlaceOfSupplyId THEN (id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) * gr.SGSTPercent / 100
                        ELSE 0
                    END
                ) as Sgst,
                SUM(
                    CASE
                        WHEN c.PlaceOfSupplyId != cl.PlaceOfSupplyId THEN (id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) * gr.IGSTPercent / 100
                        ELSE 0
                    END
                ) as Igst,
                SUM(
                    CASE
                        WHEN c.PlaceOfSupplyId = cl.PlaceOfSupplyId THEN (id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) * (COALESCE(gr.CGSTCessPercent, 0) + COALESCE(gr.SGSTCessPercent, 0)) / 100
                        ELSE (id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) * COALESCE(gr.IGSTCessPercent, 0) / 100
                    END
                ) as Cess,
                SUM(
                    (id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) +
                    CASE
                        WHEN c.PlaceOfSupplyId = cl.PlaceOfSupplyId THEN
                            (id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) * (gr.CGSTPercent + gr.SGSTPercent + COALESCE(gr.CGSTCessPercent, 0) + COALESCE(gr.SGSTCessPercent, 0)) / 100
                        ELSE
                            (id.UnitPrice * id.Quantity - COALESCE(id.NetDiscountAmount, 0)) * (gr.IGSTPercent + COALESCE(gr.IGSTCessPercent, 0)) / 100
                    END
                ) as NetAmount
            FROM
                InvoiceDetails id
                INNER JOIN Invoices i ON i.InvoiceId = id.InvoiceId
                INNER JOIN FinancialYears fy ON fy.FinancialYearId = i.FinancialYearId
                INNER JOIN Commodities com ON com.CommodityId = id.CommodityId
                INNER JOIN HsnsacCodes hsc ON hsc.HSNSACCodeId = com.HSNSACCodeId
                INNER JOIN Units u ON u.UnitId = id.UnitId
                INNER JOIN GSTRates gr ON gr.GSTRateId = id.GSTRateId
                LEFT JOIN Customers c ON c.CustomerId = i.CustomerId
                LEFT JOIN Clients cl ON cl.ClientId = i.ClientId
            WHERE
                hsc.HSNSACCode IS NOT NULL
                AND cl.ClientId = @ClientId
                AND (@FinancialYearId IS NULL OR i.FinancialYearId = @FinancialYearId)
                AND (@InvoiceMonth IS NULL OR FORMAT(i.InvoiceDate, 'MMM') = @InvoiceMonth)
                AND (@SearchText IS NULL OR hsc.HSNSACCode LIKE '%' + @SearchText + '%')
            GROUP BY
                i.FinancialYearId, fy.FinancialYearName, FORMAT(i.InvoiceDate, 'MMM'),
                hsc.HSNSACCode, hsc.HSNSACDescription, u.UnitName
            ORDER BY
                i.FinancialYearId, FORMAT(i.InvoiceDate, 'MMM'), hsc.HSNSACCode";

            var results = connection.Query<MyRow>(sql, new {
                clientId,
                FinancialYearId = financialYearId,
                InvoiceMonth = invoiceMonth,
                SearchText = searchText
            });
            response.Entities = results.ToList();
        }

        return response;
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public FileContentResult ListExcel(IDbConnection connection, ListRequest request,
        [FromServices] IHsnSummaryListHandler handler,
        [FromServices] IExcelExporter exporter,
        [FromServices] IUserAccessor userAccessor,
        [FromServices] IUserRetrieveService userRetriever)
    {
        var data = List(connection, request, handler, userAccessor, userRetriever).Entities;
        var bytes = exporter.Export(data, typeof(Columns.HsnSummaryColumns), request.ExportColumns);
        return ExcelContentResult.Create(bytes, "HsnSummaryList_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss", CultureInfo.InvariantCulture) + ".xlsx");
    }
}

