using ReconnBooks.Common.RowBehaviors;
using ReconnBooks.Modules.Common.Helpers;
using ReconnBooks.Modules.Default.Clients;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("GRNs")]
[Di<PERSON>lay<PERSON>ame("Goods Received Notes"), InstanceName("GRN"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class GrNsRow : Row<GrNsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow, IMultiClientRow
{
    const string jGRNType = nameof(jGRNType);
    const string jVendor = nameof(jVendor);
    const string jPurchaseOrder = nameof(jPurchaseOrder);
    const string jFinancialYear = nameof(jFinancialYear);
    const string jDeliveryCity = nameof(jDeliveryCity);
    const string jReceivedByEmployee = nameof(jReceivedByEmployee);
    const string jPreparedByUser = nameof(jPreparedByUser);
    const string jVerifiedByUser = nameof(jVerifiedByUser);
    const string jAuthorizedByUser = nameof(jAuthorizedByUser);
    const string jModifiedByUser = nameof(jModifiedByUser);
    const string jCancelledByUser = nameof(jCancelledByUser);
    const string jClient = nameof(jClient);

    //--Serial Numbering--

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("GRN Id"), Column("GRNId"), Identity, IdProperty]
    public int? GRNId { get => fields.GRNId[this]; set => fields.GRNId[this] = value; }

    [DisplayName("GRN No."), Column("GRNNo"), Size(50), NotNull, QuickSearch, NameProperty, Unique]
    public string GRNNo { get => fields.GRNNo[this]; set => fields.GRNNo[this] = value; }

    [DisplayName("GRN Date"), Column("GRNDate")]
    public DateTime? GRNDate { get => fields.GRNDate[this]; set => fields.GRNDate[this] = value; }

    [DisplayName("Month"), Expression("FORMAT(GRNDate, 'MMM')"), QuickFilter]
    [LookupEditor(typeof(MonthLookup))]
    public string GRNMonth { get => fields.GRNMonth[this]; set => fields.GRNMonth[this] = value; }

    [DisplayName("Vendor Name"), NotNull, ForeignKey(typeof(VendorsRow)), LeftJoin(jVendor), TextualField(nameof(VendorName))]
    [ServiceLookupEditor(typeof(VendorsRow), InplaceAdd = true, Service = "Default/Vendors/List")]
    public int? VendorId { get => fields.VendorId[this]; set => fields.VendorId[this] = value; }

    [DisplayName("GRN Type"), Column("GRNTypeId"), NotNull, ForeignKey(typeof(GrnTypesRow)), LeftJoin(jGRNType)]
    [TextualField(nameof(GRNTypeName)), ServiceLookupEditor(typeof(GrnTypesRow), InplaceAdd = true, Service = "Default/GrnTypes/List")]
    public int? GRNTypeId { get => fields.GRNTypeId[this]; set => fields.GRNTypeId[this] = value; }

    [DisplayName("GST No.")]
    [Origin(jVendor, nameof(CustomersRow.GSTIN)), LookupInclude]
    public string GSTIN { get => fields.GSTIN[this]; set => fields.GSTIN[this] = value; }

    [DisplayName("Vendor Email")]
    [Origin(jVendor, nameof(VendorsRow.EMailId)), LookupInclude]
    public string VendorEMailId { get => fields.VendorEMailId[this]; set => fields.VendorEMailId[this] = value; }

    [DisplayName("PO No."), ForeignKey(typeof(PurchaseOrdersRow)), LeftJoin(jPurchaseOrder)]
    [TextualField(nameof(PurchaseOrderNo)), ServiceLookupEditor(typeof(PurchaseOrdersRow), InplaceAdd = true, Service = "Default/PurchaseOrders/List",
        CascadeFrom = nameof(VendorId), CascadeValue = nameof(VendorId))]
    public int? PurchaseOrderId { get => fields.PurchaseOrderId[this]; set => fields.PurchaseOrderId[this] = value; }

    [DisplayName("Financial Year"), NotNull, ForeignKey(typeof(FinancialYearsRow)), LeftJoin(jFinancialYear)]
    [TextualField(nameof(FinancialYearName))]
    [LookupEditor(typeof(FinancialYearsRow))]
    public int? FinancialYearId { get => fields.FinancialYearId[this]; set => fields.FinancialYearId[this] = value; }

    [DisplayName("Financial Year"), Origin(jFinancialYear, nameof(FinancialYearsRow.FinancialYearName)), LookupInclude]
    public string FinancialYearName { get => fields.FinancialYearName[this]; set => fields.FinancialYearName[this] = value; }

    [DisplayName("Vendor DC/Inv. No."), Column("VendorDCInvoiceNo"), Size(100), NotNull]
    public string VendorDcInvoiceNo { get => fields.VendorDcInvoiceNo[this]; set => fields.VendorDcInvoiceNo[this] = value; }

    [DisplayName("DC/Inv.Date"), Column("VendorDCInvoiceDate"), NotNull]
    public DateTime? VendorDcInvoiceDate { get => fields.VendorDcInvoiceDate[this]; set => fields.VendorDcInvoiceDate[this] = value; }

    //-------------------------------------------------------------------------
    [MasterDetailRelation(foreignKey: nameof(GrnDetailsRow.GRNId)), NotMapped]
    public List<GrnDetailsRow> GrnDetailsList
    {
        get { return Fields.GrnDetailsList[this]; }
        set { Fields.GrnDetailsList[this] = value; }
    }
    //-------------------------------------------------------------------------

    [DisplayName("Shipping Through"), Size(500)]
    public string ShippedThrough { get => fields.ShippedThrough[this]; set => fields.ShippedThrough[this] = value; }

    [DisplayName("Shipping Docket No."), Size(50)]
    public string ShippingDocketNo { get => fields.ShippingDocketNo[this]; set => fields.ShippingDocketNo[this] = value; }

    [DisplayName("Delivery Address"), Size(300)]
    public string DeliveryAddress { get => fields.DeliveryAddress[this]; set => fields.DeliveryAddress[this] = value; }

    [DisplayName("Delivery City"), ForeignKey(typeof(CitiesRow)), LeftJoin(jDeliveryCity), TextualField(nameof(DeliveryCityCityName))]
    [LookupEditor(typeof(CitiesRow), Async = true)]
    public int? DeliveryCityId { get => fields.DeliveryCityId[this]; set => fields.DeliveryCityId[this] = value; }

    [DisplayName("PIN Code")]
    public int? DeliveryPinCode { get => fields.DeliveryPinCode[this]; set => fields.DeliveryPinCode[this] = value; }

    [DisplayName("Vehicle No."), Size(50)]
    public string VehicleNo { get => fields.VehicleNo[this]; set => fields.VehicleNo[this] = value; }

    [DisplayName("Vehicle Type"), Size(100)]
    public string VehicleType { get => fields.VehicleType[this]; set => fields.VehicleType[this] = value; }

    [DisplayName("Gate Pass No."), Size(100)]
    public string GatePassNo { get => fields.GatePassNo[this]; set => fields.GatePassNo[this] = value; }

    [DisplayName("Gate Pass Date")]
    public DateTime? GatePassDate { get => fields.GatePassDate[this]; set => fields.GatePassDate[this] = value; }

    [DisplayName("Inspection")]
    public string Inspection { get => fields.Inspection[this]; set => fields.Inspection[this] = value; }

    [DisplayName("Received By"), ForeignKey(typeof(EmployeesRow)), LeftJoin(jReceivedByEmployee)]
    [TextualField(nameof(ReceivedByEmployeeEmployeeName)), LookupEditor(typeof(EmployeesRow), Async = true)]
    public int? ReceivedByEmployeeId { get => fields.ReceivedByEmployeeId[this]; set => fields.ReceivedByEmployeeId[this] = value; }

    [DisplayName("Upload Documents")]
    public string UploadDocuments { get => fields.UploadDocuments[this]; set => fields.UploadDocuments[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }
 
    //--Client/Consultants MultiTenancy details--

    [DisplayName("Client"), NotNull, ForeignKey(typeof(ClientsRow)), LeftJoin(jClient), TextualField(nameof(ClientName))]
    [ServiceLookupEditor(typeof(ClientsRow), Service = "Default/Clients/List")]
    [Insertable(false), Updatable(false)] //add for MultiTenancy 
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [DisplayName("Client"), Origin(jClient, nameof(ClientsRow.ClientName))]
    public string ClientName { get => fields.ClientName[this]; set => fields.ClientName[this] = value; }
    public Int32Field ClientIdField => fields.ClientId; //Multitenancy


    [DisplayName("Prepared By User"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jPreparedByUser)]
    [TextualField(nameof(PreparedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? PreparedByUserId { get => fields.PreparedByUserId[this]; set => fields.PreparedByUserId[this] = value; }

    [DisplayName("Prepared Date"), DateTimeEditor]
    public DateTime? PreparedDate { get => fields.PreparedDate[this]; set => fields.PreparedDate[this] = value; }

    [DisplayName("Verified By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jVerifiedByUser)]
    [TextualField(nameof(VerifiedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? VerifiedByUserId { get => fields.VerifiedByUserId[this]; set => fields.VerifiedByUserId[this] = value; }

    [DisplayName("Verified Date"), DateTimeEditor]
    public DateTime? VerifiedDate { get => fields.VerifiedDate[this]; set => fields.VerifiedDate[this] = value; }

    [DisplayName("Authorized By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jAuthorizedByUser)]
    [TextualField(nameof(AuthorizedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? AuthorizedByUserId { get => fields.AuthorizedByUserId[this]; set => fields.AuthorizedByUserId[this] = value; }

    [DisplayName("Authorized Date"), DateTimeEditor]
    public DateTime? AuthorizedDate { get => fields.AuthorizedDate[this]; set => fields.AuthorizedDate[this] = value; }

    [DisplayName("Modified By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jModifiedByUser)]
    [TextualField(nameof(ModifiedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? ModifiedByUserId { get => fields.ModifiedByUserId[this]; set => fields.ModifiedByUserId[this] = value; }

    [DisplayName("Modified Date")]
    public DateTime? ModifiedDate { get => fields.ModifiedDate[this]; set => fields.ModifiedDate[this] = value; }

    [DisplayName("Cancelled By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jCancelledByUser)]
    [TextualField(nameof(CancelledByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? CancelledByUserId { get => fields.CancelledByUserId[this]; set => fields.CancelledByUserId[this] = value; }

    [DisplayName("Cancelled Date")]
    public DateTime? CancelledDate { get => fields.CancelledDate[this]; set => fields.CancelledDate[this] = value; }

    [DisplayName("Authorized Status"), NotNull]
    public bool? AuthorizedStatus { get => fields.AuthorizedStatus[this]; set => fields.AuthorizedStatus[this] = value; }

    [DisplayName("GRN Type"), Origin(jGRNType, nameof(GrnTypesRow.GRNTypeName))]
    public string GRNTypeName { get => fields.GRNTypeName[this]; set => fields.GRNTypeName[this] = value; }

    [DisplayName("Vendor Name"), Origin(jVendor, nameof(VendorsRow.VendorName))]
    public string VendorName { get => fields.VendorName[this]; set => fields.VendorName[this] = value; }

    [DisplayName("Purchase Order No"), Origin(jPurchaseOrder, nameof(PurchaseOrdersRow.PurchaseOrderNo))]
    public string PurchaseOrderNo { get => fields.PurchaseOrderNo[this]; set => fields.PurchaseOrderNo[this] = value; }

    [DisplayName("Delivery City"), Origin(jDeliveryCity, nameof(CitiesRow.CityName))]
    public string DeliveryCityCityName { get => fields.DeliveryCityCityName[this]; set => fields.DeliveryCityCityName[this] = value; }

    [DisplayName("Received By"), Origin(jReceivedByEmployee, nameof(EmployeesRow.EmployeeName))]
    public string ReceivedByEmployeeEmployeeName { get => fields.ReceivedByEmployeeEmployeeName[this]; set => fields.ReceivedByEmployeeEmployeeName[this] = value; }

    [DisplayName("Prepared By"), Origin(jPreparedByUser, nameof(Administration.UserRow.Username))]
    public string PreparedByUserUsername { get => fields.PreparedByUserUsername[this]; set => fields.PreparedByUserUsername[this] = value; }

    [DisplayName("Verified By"), Origin(jVerifiedByUser, nameof(Administration.UserRow.Username))]
    public string VerifiedByUserUsername { get => fields.VerifiedByUserUsername[this]; set => fields.VerifiedByUserUsername[this] = value; }

    [DisplayName("Authorized By"), Origin(jAuthorizedByUser, nameof(Administration.UserRow.Username))]
    public string AuthorizedByUserUsername { get => fields.AuthorizedByUserUsername[this]; set => fields.AuthorizedByUserUsername[this] = value; }

    [DisplayName("Modified By"), Origin(jModifiedByUser, nameof(Administration.UserRow.Username))]
    public string ModifiedByUserUsername { get => fields.ModifiedByUserUsername[this]; set => fields.ModifiedByUserUsername[this] = value; }

    [DisplayName("Cancelled By"), Origin(jCancelledByUser, nameof(Administration.UserRow.Username))]
    public string CancelledByUserUsername { get => fields.CancelledByUserUsername[this]; set => fields.CancelledByUserUsername[this] = value; }
}