import { GridEditorBase } from "@serenity-is/extensions";
import { stripDiacritics, isEmptyOrNull, <PERSON>rid<PERSON><PERSON>s, QuickSearchField, confirmDialog } from '@serenity-is/corelib';
import { GroupItemMetadataProvider, FrozenLayout, Column } from '@serenity-is/sleekgrid';
export class ReconnGridEditorBase<TEntity> extends GridEditorBase<TEntity, any> {

    protected searchText: string;

    protected renameGridFooterClass() {
        const footerRow = document.querySelector('.slick-footer-row');
        if (footerRow) {
            footerRow.classList.remove('slick-footer-row');
            footerRow.classList.add('slick-footerrow');
        }
    }

    protected createQuickSearchInput() {
        GridUtils.addQuickSearchInputCustom(this.toolbar.element,
            (field, searchText) => {
                this.searchText = searchText;
                this.view.setItems(this.view.getItems(), true);
            });

        const input = this.toolbar.element[0]?.querySelector('input[type="text"], input[type="search"]') as HTMLInputElement;
        if (input) {
            input.style.cursor = 'text';
        }
    }

    protected onViewFilter(item: TEntity): boolean {
        if (!super.onViewFilter(item)) {
            return false;
        }

        if (!this.searchText) {
            return true;
        }

        const searching = stripDiacritics(this.searchText).toLowerCase();
        if (isEmptyOrNull(searching)) return true;

        const match = (str: string) => str?.toLowerCase().includes(searching);

        const fields = this.getQuickSearchFields();
        for (const field of fields) {
            const value = (item as any)[field.name];
            if (value && match(value.toString())) {
                return true;
            }
        }
        return false;
    }

    protected getFrozenFields(): string[] {
        return [];
    }

    protected getDeleteColumn(): Column<TEntity> {
        return {
            field: 'DeleteRow',
            name: '',
            format: ctx =>
                `<a class="delete-row" title="Delete"> <i class="fa fa-trash" style="color: #f44336;"></i></a>`,
            width: 24,
            minWidth: 24,
            maxWidth: 24,
            resizable: false,
            sortable: false,
            frozen: true
        };
    }
    protected getColumns(): Column<TEntity>[] {
        const columns = super.getColumns();

        // Add delete column at beginning
        columns.unshift(this.getDeleteColumn());

        // Freeze selected fields
        for (const field of this.getFrozenFields()) {
            const col = columns.find(x => x.field === field);
            if (col) col.frozen = true;
        }

        return columns;
    }
    protected createSlickGrid() {
        var grid = super.createSlickGrid();

        // Register plugin for grouping support
        grid.registerPlugin(new GroupItemMetadataProvider());

        // delete row handler
        grid.onClick.subscribe((e, args) => {
            const target = e.target as HTMLElement;

            if (target && target.closest('.delete-row')) {
                e.preventDefault();

                const item = this.view.getItem(args.row);
                if (!item) return;

                confirmDialog(`Are you sure you want to delete selected record?`, () => {
                    this.view.deleteItem(item[this.getIdProperty()]);
                    this.view.refresh();
                });
            }
        });

        return grid;
    }

    protected getSlickOptions() {
        var opt = super.getSlickOptions();
        opt.showFooterRow = true;
        opt.layoutEngine = new FrozenLayout();
        return opt;
    }
}
