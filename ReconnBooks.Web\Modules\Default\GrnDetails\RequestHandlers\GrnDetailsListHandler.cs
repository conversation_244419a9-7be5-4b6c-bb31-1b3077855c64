﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.GrnDetailsRow>;
using MyRow = ReconnBooks.Default.GrnDetailsRow;

namespace ReconnBooks.Default;

public interface IGrnDetailsListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class GrnDetailsListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IGrnDetailsListHandler
{
    public GrnDetailsListHandler(IRequestContext context)
            : base(context)
    {
    }
}