using ReconnBooks.Default;
using System.Collections;
using ReconnBooks.Modules.Administration.User.Authentication.Claims;
using ReconnBooks.Modules.Default.Clients;
using ReconnBooks.Modules.Common.RowBehaviors;

namespace ReconnBooks.Modules.Common.Helpers;

[LookupScript]
public class MonthLookup : RowLookupScript<InvoicesRow>
{
    public MonthLookup(ISqlConnections sqlConnections)
        : base(sqlConnections)
    {
    }

    protected override IEnumerable GetItems()
    {
        return new List<string>
        {
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec"
        };
    }
}