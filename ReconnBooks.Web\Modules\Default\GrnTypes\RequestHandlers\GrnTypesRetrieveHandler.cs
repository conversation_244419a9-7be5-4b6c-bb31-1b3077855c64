﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.GrnTypesRow>;
using MyRow = ReconnBooks.Default.GrnTypesRow;

namespace ReconnBooks.Default;

public interface IGrnTypesRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class GrnTypesRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IGrnTypesRetrieveHandler
{
    public GrnTypesRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}