﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.GrnDetailsRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.GrnDetailsRow;

namespace ReconnBooks.Default;

public interface IGrnDetailsSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class GrnDetailsSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IGrnDetailsSaveHandler
{
    public GrnDetailsSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}