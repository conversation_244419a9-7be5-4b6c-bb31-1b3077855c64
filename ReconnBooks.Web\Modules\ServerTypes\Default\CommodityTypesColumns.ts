﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { CommodityTypesRow } from "./CommodityTypesRow";

export interface CommodityTypesColumns {
    RowNumber: Column<CommodityTypesRow>;
    CommodityTypeId: Column<CommodityTypesRow>;
    CommodityType: Column<CommodityTypesRow>;
}

export class CommodityTypesColumns extends ColumnsBase<CommodityTypesRow> {
    static readonly columnsKey = 'Default.CommodityTypes';
    static readonly Fields = fieldsProxy<CommodityTypesColumns>();
}