using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("GRNTypes")]
[DisplayName("GRN Types"), InstanceName("GRN Types"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class GrnTypesRow : Row<GrnTypesRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    //--Serial Numbering--

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Grn Type Id"), Column("GRNTypeId"), Identity, IdProperty]
    public int? GRNTypeId { get => fields.GRNTypeId[this]; set => fields.GRNTypeId[this] = value; }

    [DisplayName("GRN Type Name"), Column("GRNTypeName"), Size(50), NotNull, QuickSearch, NameProperty]
    public string GRNTypeName { get => fields.GRNTypeName[this]; set => fields.GRNTypeName[this] = value; }

    [DisplayName("Description"), Size(500)]
    public string Description { get => fields.Description[this]; set => fields.Description[this] = value; }
}