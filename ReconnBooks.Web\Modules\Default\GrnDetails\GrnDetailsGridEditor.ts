import { CommoditiesRow, CommodityTypesRow, GrnDetailsColumns, GrnDetailsRow, UnitsRow } from '@/ServerTypes/Default';
import { Decorators, toId, confirmDialog, ToolButton } from '@serenity-is/corelib';
import { GridEditorBase } from '@serenity-is/extensions';
import { GrnDetailsDialog } from './GrnDetailsDialog';
import { Column } from '@serenity-is/sleekgrid';
import { ReconnGridEditorBase } from '../../Common/Helpers/ReconnGridEditorBase';
import { QuickSearchField } from "@serenity-is/corelib";
import { DetailsGridExcelExportButton } from '../../Common/Helpers/DetailsGridExcelExportButton';

@Decorators.registerEditor('ReconnBooks.Default.GrnDetailsGridEditor')
export class GrnDetailsGridEditor extends ReconnGridEditorBase<GrnDetailsRow> {
    protected getColumnsKey() { return GrnDetailsColumns.columnsKey; }
    protected getDialogType() { return GrnDetailsDialog; }
    protected getLocalTextDbPrefix() { return GrnDetailsRow.localTextPrefix; }

    protected getQuickSearchFields(): QuickSearchField[] {
        return [
            { name: 'CommodityName', title: 'Commodity Name' },
            { name: 'CommodityDescription', title: 'Commodity Description' }
        ];
    }

    protected getButtons(): ToolButton[] {
        const buttons = super.getButtons();
        // Get the button with name 'Add'
        const addButton = buttons.find(button => button.title === 'Add');

        //check if addButton exists before setting OnClick
        if (addButton) {
            addButton.onClick = () => {
                const dialog = new GrnDetailsDialog({}); // Pass current items in the grid to the child dialog

                dialog.onSave = (newItem, callback) => {

                    var newEntity = newItem.request.Entity;
                    // Check for duplicates before saving
                    const existingItems = this.view.getItems();
                    const currentlyExistingItem = existingItems.find(item => item.CommodityId == newEntity.CommodityId && item.CommodityDescription == newEntity.CommodityDescription);

                    if (currentlyExistingItem) {
                        // Show confirmation dialog for handling duplicates
                        confirmDialog(
                            `An item with the name "${currentlyExistingItem.CommodityName}" already exists. Do you want to add the quantity (${newEntity.Quantity}) to the existing item?`,
                            () => {
                                // User clicked "Yes" - Update existing item
                                currentlyExistingItem.PoQuantity += newEntity.PoQuantity;

                                const id = currentlyExistingItem[this.getIdProperty()];

                                this.view.updateItem(id, currentlyExistingItem); // Update the grid
                                this.view.refresh(); // Refresh the grid view
                                dialog.dialogClose("save-and-close"); // Close the dialog
                            },
                            {
                                onNo: () => {
                                    // User clicked "No" - Do nothing
                                    //console.log("Duplicate addition canceled.");
                                    //dialog.load()
                                }
                            }
                        );
                        return;
                    }

                    // No duplicates, proceed with normal save operation
                    this.save(newItem, callback);

                    // Update the RowNumber - the last item in the grid to match its position
                    (this.view.getItems()[this.view.getLength() - 1]).RowNumber = this.view.getLength();

                    dialog.dialogClose();
                };
                dialog.loadEntityAndOpenDialog(this.getNewEntity());
            };
        }

        buttons.push(DetailsGridExcelExportButton({
            grid: this,
            fileName: 'GrNDetails',
            hint: 'Export current view to Excel',
            title: 'Excel',
            separator: true
        }));

        return buttons;
    }

    protected getAddButtonCaption() {
        return "Add";
    }

    protected getFrozenFields(): string[] {
        return ['RowNumber', 'CommodityName'];
    }

    protected validateEntity(row: GrnDetailsRow, id: number): boolean {

        id ??= row[this.getIdProperty()];

        if (!row.CommodityTypeId || !row.CommodityId || !row.PoUnitId) {
            console.error(`Validation failed for row ${id}: Required fields are missing.`);
            return false; // Return false if mandatory fields are missing
        }

        row.CommodityTypeId = toId(row.CommodityTypeId);
        CommodityTypesRow.getLookupAsync().then(lookup => {
            var item = this.view?.getItemById?.(id);
            if (item) {
                item.CommodityType = lookup.itemById[row.CommodityTypeId].CommodityType;
                this.view.updateItem(id, item);
            }
        })

        row.CommodityId = toId(row.CommodityId);
        CommoditiesRow.getLookupAsync().then(lookup => {
            var item = this.view?.getItemById?.(id);
            if (item) {
                item.CommodityName = lookup.itemById[row.CommodityId].CommodityName;
                item.CommodityCode = lookup.itemById[row.CommodityId].CommodityCode;
                //item.RowNumber = this.view.getLength();
                this.view.updateItem(id, item);
            }
        })
        
        row.ReceivedUnitId = toId(row.ReceivedUnitId);
        row.PoUnitId = toId(row.PoUnitId);
        row.AcceptedUnitId = toId(row.AcceptedUnitId);
        UnitsRow.getLookupAsync().then(lookup => {
            var item = this.view?.getItemById?.(id);
            if (item) {
                item.ReceivedUnitUnitName = lookup.itemById[row.ReceivedUnitId].UnitName;
                item.PoUnitUnitName = lookup.itemById[row.PoUnitId].UnitName;
                item.AcceptedUnitUnitName = lookup.itemById[row.AcceptedUnitId].UnitName;
                this.view.updateItem(id, item);
            }
        });
        return true;
    }
}