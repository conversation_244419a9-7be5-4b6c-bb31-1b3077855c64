﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.GrnTypesRow>;
using MyRow = ReconnBooks.Default.GrnTypesRow;

namespace ReconnBooks.Default;

public interface IGrnTypesListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class GrnTypesListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IGrnTypesListHandler
{
    public GrnTypesListHandler(IRequestContext context)
            : base(context)
    {
    }
}