﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface CommodityTypesForm {
    CommodityType: StringEditor;
}

export class CommodityTypesForm extends PrefixedContext {
    static readonly formKey = 'Default.CommodityTypes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CommodityTypesForm.init)  {
            CommodityTypesForm.init = true;

            var w0 = StringEditor;

            initFormType(CommodityTypesForm, [
                'CommodityType', w0
            ]);
        }
    }
}