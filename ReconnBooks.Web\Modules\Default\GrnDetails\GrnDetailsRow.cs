using ReconnBooks.Common.RowBehaviors;
using ReconnBooks.Modules.Default.Clients;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("GRNDetails")]
[DisplayName("GRN Details"), InstanceName("GRN Detail"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class GrnDetailsRow : Row<GrnDetailsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow, IMultiClientRow
{
    const string jGRN = nameof(jGRN);
    const string jPurchaseOrderDetail = nameof(jPurchaseOrderDetail);
    const string jCommodityType = nameof(jCommodityType);
    const string jCommodity = nameof(jCommodity);
    const string jPoUnit = nameof(jPoUnit);
    const string jReceivedUnit = nameof(jReceivedUnit);
    const string jAcceptedUnit = nameof(jAcceptedUnit);
    const string jLocation = nameof(jLocation);
    const string jWarehouse = nameof(jWarehouse);
    const string jStore = nameof(jStore);
    const string jRack = nameof(jRack);
    const string jClient = nameof(jClient);

    //--Serial Numbering--

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Grn Detail Id"), Column("GRNDetailId"), Identity, IdProperty, NameProperty]
    public int? GrnDetailId { get => fields.GrnDetailId[this]; set => fields.GrnDetailId[this] = value; }

    [DisplayName("GRN No."), Column("GRNId"), NotNull, ForeignKey(typeof(GrNsRow)), LeftJoin(jGRN), TextualField(nameof(GRNNo))]
    [ServiceLookupEditor(typeof(GrNsRow), Service = "Default/GRNs/List")]
    public int? GRNId { get => fields.GRNId[this]; set => fields.GRNId[this] = value; }

    [DisplayName("PO Detail"), ForeignKey(typeof(PurchaseOrderDetailsRow)), LeftJoin(jPurchaseOrderDetail)]
    [TextualField(nameof(PurchaseOrderDetailCommodityDescription)), LookupEditor(typeof(PurchaseOrderDetailsRow), Async = true)]
    public int? PurchaseOrderDetailId { get => fields.PurchaseOrderDetailId[this]; set => fields.PurchaseOrderDetailId[this] = value; }

    //--Commodities--

    [DisplayName("Commodity Type"), NotNull, ForeignKey(typeof(CommodityTypesRow)), LeftJoin(jCommodityType)]
    [TextualField(nameof(CommodityType))]
    [ServiceLookupEditor(typeof(CommodityTypesRow), Service = "Default/CommodityTypes/List")]
    public int? CommodityTypeId { get => fields.CommodityTypeId[this]; set => fields.CommodityTypeId[this] = value; }

    [DisplayName("Commodity"), NotNull, ForeignKey(typeof(CommoditiesRow)), LeftJoin(jCommodity), TextualField(nameof(CommodityName))]
    [ServiceLookupEditor(typeof(CommoditiesRow), InplaceAdd = true, Service = "Default/Commodities/List",CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    public long? CommodityId { get => fields.CommodityId[this]; set => fields.CommodityId[this] = value; }

    //---- Fetching Product Code from Products Master -------------
    [DisplayName("Product Code")]
    [Origin(jCommodity, nameof(CommoditiesRow.CommodityCode)), TextualField(nameof(CommodityCode)), LookupInclude]
    [CommodityCodeEditor(CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    public string CommodityCode { get => fields.CommodityCode[this]; set => fields.CommodityCode[this] = value; }

    //--Commodity details--

    [DisplayName("Commodity Description"), QuickSearch]
    public string CommodityDescription { get => fields.CommodityDescription[this]; set => fields.CommodityDescription[this] = value; }

    [DisplayName("Commodity Type"), Origin(jCommodityType, nameof(CommodityTypesRow.CommodityType)), LookupInclude]
    public string CommodityType { get => fields.CommodityType[this]; set => fields.CommodityType[this] = value; }

    [DisplayName("Commodity Name"), Origin(jCommodity, nameof(CommoditiesRow.CommodityName)), LookupInclude]
    public string CommodityName { get => fields.CommodityName[this]; set => fields.CommodityName[this] = value; }

    [DisplayName("PO Quantity"), Column("POQuantity"), Size(18), Scale(2)]
    public decimal? PoQuantity { get => fields.PoQuantity[this]; set => fields.PoQuantity[this] = value; }

    [DisplayName("PO Unit"), Column("POUnitId"), ForeignKey(typeof(UnitsRow)), LeftJoin(jPoUnit), TextualField(nameof(PoUnitUnitName))]
    [LookupEditor(typeof(UnitsRow), Async = true)]
    public int? PoUnitId { get => fields.PoUnitId[this]; set => fields.PoUnitId[this] = value; }

    [DisplayName("Received Quantity"), Size(18), Scale(2), NotNull]
    public decimal? ReceivedQuantity { get => fields.ReceivedQuantity[this]; set => fields.ReceivedQuantity[this] = value; }

    [DisplayName("Received Unit"), NotNull, ForeignKey(typeof(UnitsRow)), LeftJoin(jReceivedUnit)]
    [TextualField(nameof(ReceivedUnitUnitName)), LookupEditor(typeof(UnitsRow), Async = true)]
    public int? ReceivedUnitId { get => fields.ReceivedUnitId[this]; set => fields.ReceivedUnitId[this] = value; }

    [DisplayName("SKU"), Column("SKU"), Size(300)]
    public string Sku { get => fields.Sku[this]; set => fields.Sku[this] = value; }

    [DisplayName("Serial Nos.")]
    public string SerialNos { get => fields.SerialNos[this]; set => fields.SerialNos[this] = value; }

    [DisplayName("Accepted Quantity"), Size(18), Scale(2)]
    public decimal? AcceptedQuantity { get => fields.AcceptedQuantity[this]; set => fields.AcceptedQuantity[this] = value; }

    [DisplayName("Accepted Unit"), ForeignKey(typeof(UnitsRow)), LeftJoin(jAcceptedUnit), TextualField(nameof(AcceptedUnitUnitName))]
    [LookupEditor(typeof(UnitsRow), Async = true)]
    public int? AcceptedUnitId { get => fields.AcceptedUnitId[this]; set => fields.AcceptedUnitId[this] = value; }

    [DisplayName("Supply Due Date")]
    public DateTime? SupplyDueDate { get => fields.SupplyDueDate[this]; set => fields.SupplyDueDate[this] = value; }

    [DisplayName("Location"), ForeignKey(typeof(LocationsRow)), LeftJoin(jLocation), TextualField(nameof(LocationName))]
    [ServiceLookupEditor(typeof(LocationsRow), Service = "Default/Locations/List")]
    public int? LocationId { get => fields.LocationId[this]; set => fields.LocationId[this] = value; }

    [DisplayName("Warehouse"), ForeignKey(typeof(WarehousesRow)), LeftJoin(jWarehouse), TextualField(nameof(WarehouseName))]
    [ServiceLookupEditor(typeof(WarehousesRow), Service = "Default/Warehouses/List")]
    public int? WarehouseId { get => fields.WarehouseId[this]; set => fields.WarehouseId[this] = value; }

    [DisplayName("Store Name"), ForeignKey(typeof(StoresRow)), LeftJoin(jStore), TextualField(nameof(StoreName))]
    [ServiceLookupEditor(typeof(StoresRow), Service = "Default/Stores/List")]
    public int? StoreId { get => fields.StoreId[this]; set => fields.StoreId[this] = value; }

    [DisplayName("Rack No."), ForeignKey(typeof(RacksRow)), LeftJoin(jRack), TextualField(nameof(RackNo))]
    [ServiceLookupEditor(typeof(RacksRow), Service = "Default/Racks/List")]
    public int? RackId { get => fields.RackId[this]; set => fields.RackId[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }

    //--Client/Consultants MultiTenancy details--

    [DisplayName("Client"), NotNull, ForeignKey(typeof(ClientsRow)), LeftJoin(jClient), TextualField(nameof(ClientName))]
    [ServiceLookupEditor(typeof(ClientsRow), Service = "Default/Clients/List")]
    [Insertable(false), Updatable(false)] //add for MultiTenancy 
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [DisplayName("Client"), Origin(jClient, nameof(ClientsRow.ClientName))]
    public string ClientName { get => fields.ClientName[this]; set => fields.ClientName[this] = value; }
    public Int32Field ClientIdField => fields.ClientId; //Multitenancy

    [DisplayName("GRN No."), Origin(jGRN, nameof(GrNsRow.GRNNo))]
    public string GRNNo { get => fields.GRNNo[this]; set => fields.GRNNo[this] = value; }

    [DisplayName("Commodity Description")]
    [Origin(jPurchaseOrderDetail, nameof(PurchaseOrderDetailsRow.CommodityDescription))]
    public string PurchaseOrderDetailCommodityDescription { get => fields.PurchaseOrderDetailCommodityDescription[this]; set => fields.PurchaseOrderDetailCommodityDescription[this] = value; }

    [DisplayName("PO Unit"), Origin(jPoUnit, nameof(UnitsRow.UnitName)), LookupInclude]
    public string PoUnitUnitName { get => fields.PoUnitUnitName[this]; set => fields.PoUnitUnitName[this] = value; }

    [DisplayName("Received Unit"), Origin(jReceivedUnit, nameof(UnitsRow.UnitName)), LookupInclude]
    public string ReceivedUnitUnitName { get => fields.ReceivedUnitUnitName[this]; set => fields.ReceivedUnitUnitName[this] = value; }

    [DisplayName("Accepted Unit"), Origin(jAcceptedUnit, nameof(UnitsRow.UnitName)), LookupInclude]
    public string AcceptedUnitUnitName { get => fields.AcceptedUnitUnitName[this]; set => fields.AcceptedUnitUnitName[this] = value; }

    [DisplayName("Location Name"), Origin(jLocation, nameof(LocationsRow.LocationName))]
    public string LocationName { get => fields.LocationName[this]; set => fields.LocationName[this] = value; }

    [DisplayName("Warehouse Name"), Origin(jWarehouse, nameof(WarehousesRow.WarehouseName))]
    public string WarehouseName { get => fields.WarehouseName[this]; set => fields.WarehouseName[this] = value; }

    [DisplayName("Store Name"), Origin(jStore, nameof(StoresRow.StoreName)), LookupInclude]
    public string StoreName { get => fields.StoreName[this]; set => fields.StoreName[this] = value; }

    [DisplayName("Rack No"), Origin(jRack, nameof(RacksRow.RackNo))]
    public string RackNo { get => fields.RackNo[this]; set => fields.RackNo[this] = value; }
}