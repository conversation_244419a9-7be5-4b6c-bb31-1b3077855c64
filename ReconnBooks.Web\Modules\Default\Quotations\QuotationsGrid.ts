import { QuotationsColumns, QuotationsRow, QuotationsService } from '@/ServerTypes/Default';
import { alertDialog, Decorators, EntityGrid, GridRowSelectionMixin, LookupEditor, WidgetProps, notify<PERSON><PERSON><PERSON>, notify<PERSON><PERSON><PERSON>, Widget } from '@serenity-is/corelib';
import { ExcelExportHelper, ReportHelper, PdfExportHelper } from "@serenity-is/extensions";
import { HeaderFiltersMixin } from "@serenity-is/pro.extensions";
import { BulkReportAction } from '../../Common/Reporting/BulkReportAction';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { QuotationsDialog } from './QuotationsDialog';

@Decorators.registerClass('ReconnBooks.Default.QuotationsGrid')
@Decorators.filterable()
export class QuotationsGrid extends EntityGrid<QuotationsRow, any> {
    protected getColumnsKey() { return QuotationsColumns.columnsKey; }
    protected getDialogType() { return QuotationsDialog; }
    protected getRowDefinition() { return QuotationsRow; }
    protected getService() { return QuotationsService.baseUrl; }

    private rowSelection: GridRowSelectionMixin;

    constructor(props: WidgetProps<any>) {
        super(props);

        new HeaderFiltersMixin({
            grid: this
        });

    }

    protected createToolbarExtensions() {
        super.createToolbarExtensions();
        this.rowSelection = new GridRowSelectionMixin(this);
    }

    protected async createQuickFilters(): Promise<void> {
        await super.createQuickFilters();

        // Set current financial year
        const currentFinancialYearId = await FinancialYearHelper.getCurrentFinancialYearId();
        if (currentFinancialYearId) {
            this.findQuickFilter(LookupEditor, "FinancialYearId").values = [currentFinancialYearId.toString()];
        }
    }

    protected getColumns() {
        var columns = super.getColumns();

        columns.splice(0, 0, GridRowSelectionMixin.createSelectColumn(() => this.rowSelection));

        columns.splice(1, 0, {
            id: 'Print Quotation',
            field: null,
            name: '',
            cssClass: 'align-center',
            headerCssClass: 'no-header-filter',
            format: _ => `<a class="inline-action" data-action="print-quotation" title="quotation"><i class="fas fa-file-pdf" style="color: red"></i></a>`,
            width: 20,
            minWidth: 20,
            maxWidth: 20
        });

        // Email Quotation column
        columns.splice(2, 0, {
            id: 'Email Quotation',
            field: null,
            name: '',
            cssClass: 'align-center',
            headerCssClass: 'no-header-filter',
            format: _ => `<a class="inline-action" data-action="email-quotation" title="email quotation"><i class="fas fa-envelope" style="color: #4CAF50"></i></a>`,
            width: 20,
            minWidth: 20,
            maxWidth: 20
        });

        return columns;
    }

    protected onClick(e: Event, row: number, cell: number) {
        super.onClick(e, row, cell);

        //if (Fluent.isDefaultPrevented(e))
        //    return;

        var item = this.itemAt(row);
        let action = (e.target as HTMLElement)?.closest(".inline-action")?.getAttribute("data-action");
        if (action) {
            e.preventDefault();
            if (action == "print-quotation") {
                ReportHelper.execute({
                    reportKey: 'QuotationReport',
                    params: {
                        ID: item.QuotationId
                    }
                });
            }
            else if (action == "email-quotation") {
                this.emailQuotation(item);
            }
        }
    }

    private async emailQuotation(item: QuotationsRow) {
        try {
            await QuotationsService.EmailQuotation({
                DocumentId: item.QuotationId,
                DocumentNo: item.QuotationNo,
                ToEmail: item.CustomerEMailId
            });
            notifySuccess("Quotation has been emailed successfully");
        }
        catch (e) {
            notifyError(e.message);
        }
    }

    protected getButtons() {
        var buttons = super.getButtons();

        const addButton = buttons.find(x => x.cssClass === "add-button");
        if (addButton) {
            addButton.title = "New Quotation";
        }

        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: QuotationsService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));

        buttons.push(PdfExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            title: "PDF"
        }));

        buttons.push({
            title: 'Download',
            separator: true,
            cssClass: 'send-button',
            icon: 'fa fa-download',
            onClick: () => {
                if (!this.onViewSubmit()) {
                    return;
                }
                const selectedKeys = this.rowSelection.getSelectedKeys();

                if (selectedKeys.length === 0) {
                    alertDialog("Please select some records to download");
                    return;
                }
                var action = new BulkReportAction({
                    key: 'QuotationReport',
                    pdfFileName: 'Quotation',
                    zippedFileName: 'Quotations',
                });
                action.execute(this.rowSelection.getSelectedKeys());
                action.done = () => this.rowSelection.resetCheckedAndRefresh();
            }
        });

        return buttons;
    }

    //protected createEntityDialog(itemType: string, callback?: (dlg: any) => void): any {
    //    const dialog = super.createEntityDialog(itemType, callback);

    //    (dialog as QuotationsDialog).onClose(() => {
    //        setTimeout(() => {
    //            let addButton = this.getButtons().find(a => a.title === "New Quotation");
    //            (addButton as Element)..focus();
    //            if (addButton && typeof (addButton as any).focus === 'function') {
    //                (addButton as any).focus();
    //            }
    //        }, 50);
    //    });

    //    return dialog;
    //}

    protected initEntityDialog(itemType: string, dialog: Widget<any>): void {
        super.initEntityDialog(itemType, dialog);

        //dialog.element.focus();

        (dialog as QuotationsDialog).onClose(() => {

            let addButton = this.getButtons().find(a => a.title === "New Quotation");

        });
    }


}