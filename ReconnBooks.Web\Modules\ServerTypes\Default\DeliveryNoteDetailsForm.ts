﻿import { ServiceLookupEditor, TextAreaEditor, StringEditor, DecimalEditor, LookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";
import { UnitsDialog } from "../../Default/Units/UnitsDialog";

export interface DeliveryNoteDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    Sku: StringEditor;
    HSNSACCode: StringEditor;
    HSNSACGroup: StringEditor;
    HSNSACDescription: TextAreaEditor;
    Quantity: DecimalEditor;
    UnitId: ServiceLookupEditor;
    UnitPrice: DecimalEditor;
    GSTRateId: LookupEditor;
    DummyField1: DecimalEditor;
    UnitAmount: DecimalEditor;
    IGSTRate: DecimalEditor;
    PerUnitIGSTAmount: DecimalEditor;
    IGSTAmount: DecimalEditor;
    CGSTRate: DecimalEditor;
    PerUnitCGSTAmount: DecimalEditor;
    CGSTAmount: DecimalEditor;
    SGSTRate: DecimalEditor;
    PerUnitSGSTAmount: DecimalEditor;
    SGSTAmount: DecimalEditor;
    DummyField: StringEditor;
    PerUnitPrice: DecimalEditor;
    NetAmount: DecimalEditor;
}

export class DeliveryNoteDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.DeliveryNoteDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DeliveryNoteDetailsForm.init)  {
            DeliveryNoteDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = StringEditor;
            var w4 = DecimalEditor;
            var w5 = LookupEditor;

            initFormType(DeliveryNoteDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'Sku', w3,
                'HSNSACCode', w3,
                'HSNSACGroup', w3,
                'HSNSACDescription', w2,
                'Quantity', w4,
                'UnitId', w0,
                'UnitPrice', w4,
                'GSTRateId', w5,
                'DummyField1', w4,
                'UnitAmount', w4,
                'IGSTRate', w4,
                'PerUnitIGSTAmount', w4,
                'IGSTAmount', w4,
                'CGSTRate', w4,
                'PerUnitCGSTAmount', w4,
                'CGSTAmount', w4,
                'SGSTRate', w4,
                'PerUnitSGSTAmount', w4,
                'SGSTAmount', w4,
                'DummyField', w3,
                'PerUnitPrice', w4,
                'NetAmount', w4
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog, UnitsDialog]); // referenced dialogs